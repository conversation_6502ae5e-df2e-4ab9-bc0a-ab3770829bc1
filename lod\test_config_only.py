"""
测试重构后的LOD配置系统（不依赖pxr）
验证中心化配置功能
"""

import os
import sys
import json

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

def test_centralized_config_only():
    """测试中心化配置系统（不依赖pxr）"""
    print("=== 测试中心化配置系统 ===")
    
    try:
        # 直接导入配置模块，避免导入依赖pxr的模块
        from lod.lod_config import CentralizedLODConfig, get_default_lod_config, create_lod_config_from_tileset
        
        # 测试默认配置
        default_config = get_default_lod_config()
        print(f"✓ 默认配置创建成功")
        print(f"  最大屏幕误差: {default_config.maximum_screen_space_error}")
        print(f"  相机路径: {default_config.camera_path}")
        print(f"  屏幕宽度: {default_config.screen_width}")
        print(f"  水平视野角: {default_config.horizontal_fov}")
        
        # 测试几何误差映射
        test_errors = [32, 16, 8, 4, 2, 1]
        print(f"  几何误差到LOD级别映射:")
        for error in test_errors:
            lod_level = default_config.get_lod_level_from_geometric_error(error)
            numeric_lod = default_config.get_lod_level_numeric(error)
            print(f"    {error} -> {lod_level} (数字级别: {numeric_lod})")
        
        # 测试反向映射
        print(f"  数字LOD级别到几何误差映射:")
        for numeric_lod in [20, 18, 15]:
            geometric_error = default_config.get_geometric_error_from_numeric_lod(numeric_lod)
            lod_name = default_config.get_lod_name_from_numeric(numeric_lod)
            print(f"    {numeric_lod} -> {geometric_error} ({lod_name})")
        
        # 测试从tileset创建配置
        tileset_path = "lod/tileset_data/tileset.json"
        if os.path.exists(tileset_path):
            tileset_config = create_lod_config_from_tileset(tileset_path)
            print(f"✓ 从tileset创建配置成功")
            print(f"  几何误差映射: {tileset_config.get_lod_mapping()}")
            print(f"  所有几何误差值: {tileset_config.get_all_geometric_errors()}")
        else:
            print(f"⚠ Tileset文件不存在: {tileset_path}")
            # 创建一个模拟的tileset配置
            mock_config = CentralizedLODConfig()
            print(f"✓ 使用默认配置")
            print(f"  几何误差映射: {mock_config.get_lod_mapping()}")
        
        return True
        
    except Exception as e:
        print(f"✗ 中心化配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_save_load():
    """测试配置保存和加载"""
    print("\n=== 测试配置保存和加载 ===")
    
    try:
        from lod.lod_config import CentralizedLODConfig
        
        # 创建配置
        config = CentralizedLODConfig()
        
        # 修改一些配置
        config.maximum_screen_space_error = 64.0
        config.screen_width = 2560
        config.horizontal_fov = 90.0
        
        # 保存配置
        test_config_path = "test_lod_config.json"
        config.save_config(test_config_path)
        print(f"✓ 配置保存成功: {test_config_path}")
        
        # 加载配置
        new_config = CentralizedLODConfig()
        new_config.load_config(test_config_path)
        print(f"✓ 配置加载成功")
        
        # 验证配置
        if (new_config.maximum_screen_space_error == 64.0 and
            new_config.screen_width == 2560 and
            new_config.horizontal_fov == 90.0):
            print(f"✓ 配置保存和加载正确")
        else:
            print(f"✗ 配置保存和加载不正确")
            return False
        
        # 清理测试文件
        if os.path.exists(test_config_path):
            os.remove(test_config_path)
            print(f"✓ 清理测试文件")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置保存和加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_lod_system_config_compatibility():
    """测试LODSystemConfig向后兼容性"""
    print("\n=== 测试LODSystemConfig向后兼容性 ===")
    
    try:
        from lod.lod_config import LODSystemConfig, LODLevel, LODConfig
        
        # 测试传统LODSystemConfig
        old_config = LODSystemConfig()
        print(f"✓ 传统LODSystemConfig创建成功")
        
        # 测试LODConfig结构（应该移除了distance_threshold）
        lod_config = old_config.lod_configs[LODLevel.HIGH]
        print(f"  LODConfig属性: {[attr for attr in dir(lod_config) if not attr.startswith('_')]}")
        
        # 验证distance_threshold已移除
        if not hasattr(lod_config, 'distance_threshold'):
            print(f"✓ 确认移除了distance_threshold参数")
        else:
            print(f"⚠ distance_threshold参数仍然存在")
        
        # 验证新增的geometric_error参数
        if hasattr(lod_config, 'geometric_error'):
            print(f"✓ 确认添加了geometric_error参数")
        else:
            print(f"✗ 缺少geometric_error参数")
            return False
        
        # 测试get_optimal_lod_level方法
        lod_level = old_config.get_optimal_lod_level(screen_error=2.0, geometric_error=1.0)
        print(f"✓ get_optimal_lod_level方法正常工作: {lod_level}")
        
        # 测试中心化配置集成
        if hasattr(old_config, 'centralized_config'):
            print(f"✓ 集成了中心化配置")
            print(f"  配置类型: {type(old_config.centralized_config)}")
        else:
            print(f"⚠ 未集成中心化配置")
        
        return True
        
    except Exception as e:
        print(f"✗ LODSystemConfig兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_geometric_error_calculations():
    """测试几何误差计算"""
    print("\n=== 测试几何误差计算 ===")
    
    try:
        from lod.lod_config import CentralizedLODConfig
        
        config = CentralizedLODConfig()
        
        # 测试数字LOD级别计算
        test_cases = [
            (1.0, "High"),
            (2.0, "High"),
            (4.0, "Medium"),
            (8.0, "Medium"),
            (16.0, "Medium"),
            (32.0, "Low")
        ]
        
        print("  几何误差 -> LOD级别映射测试:")
        all_correct = True
        for geometric_error, expected_lod in test_cases:
            actual_lod = config.get_lod_level_from_geometric_error(geometric_error)
            numeric_lod = config.get_lod_level_numeric(geometric_error)
            
            print(f"    {geometric_error} -> {actual_lod} (数字: {numeric_lod})")
            
            if actual_lod != expected_lod:
                print(f"    ✗ 期望: {expected_lod}, 实际: {actual_lod}")
                all_correct = False
        
        if all_correct:
            print("✓ 几何误差计算正确")
        else:
            print("✗ 几何误差计算有误")
            return False
        
        # 测试反向计算
        print("  数字LOD级别 -> 几何误差反向计算:")
        for numeric_lod in [20, 19, 18, 17, 16, 15]:
            geometric_error = config.get_geometric_error_from_numeric_lod(numeric_lod)
            lod_name = config.get_lod_name_from_numeric(numeric_lod)
            print(f"    {numeric_lod} -> {geometric_error} ({lod_name})")
        
        return True
        
    except Exception as e:
        print(f"✗ 几何误差计算测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """运行所有测试"""
    print("开始测试重构后的LOD配置系统...")
    print("=" * 60)
    
    tests = [
        test_centralized_config_only,
        test_config_save_load,
        test_lod_system_config_compatibility,
        test_geometric_error_calculations
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有配置测试通过！重构成功！")
        return True
    else:
        print("❌ 部分测试失败，需要修复")
        return False

if __name__ == "__main__":
    main()
