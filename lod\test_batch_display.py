"""
测试分批显示功能
用于验证瓦片按顺序分批显示的效果
"""

import sys
import os
import time

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

def test_batch_display_in_script_editor():
    """在Script Editor中测试分批显示功能"""
    print("=== Tile Batch Display Test ===")
    
    try:
        # 导入必要的模块
        from simple_tileset_lod_example_standalone import (
            TilesetConfig,
            get_tileset_region_bounds_from_stage,
            update_tileset_lod_visibility_hierarchical
        )
        from lod_scheduler import LODScheduler
        import omni.usd
        
        # 获取当前stage
        stage = omni.usd.get_context().get_stage()
        if not stage:
            print("❌ ERROR: No stage available. Please load a USD scene first.")
            return False
        
        print("✅ Stage found")
        
        # 获取tileset区域边界
        region_bounds = get_tileset_region_bounds_from_stage()
        if not region_bounds:
            print("❌ ERROR: Could not find tileset region bounds in stage.")
            return False
        
        print("✅ Tileset region bounds found")
        
        # 创建配置
        config = TilesetConfig()
        
        # 测试不同的分批显示配置
        test_configs = [
            {"enabled": False, "batch_size": 1, "interval": 0.1, "name": "Disabled"},
            {"enabled": True, "batch_size": 1, "interval": 0.2, "name": "Single tile per batch"},
            {"enabled": True, "batch_size": 2, "interval": 0.15, "name": "Two tiles per batch"},
            {"enabled": True, "batch_size": 3, "interval": 0.1, "name": "Three tiles per batch"},
        ]
        
        for i, test_config in enumerate(test_configs):
            print(f"\n" + "="*60)
            print(f"TEST {i+1}: {test_config['name']}")
            print("="*60)
            
            # 配置分批显示
            config.enable_batch_display = test_config["enabled"]
            config.batch_size = test_config["batch_size"]
            config.batch_interval = test_config["interval"]
            
            print(f"Configuration:")
            print(f"  Enabled: {config.enable_batch_display}")
            print(f"  Batch size: {config.batch_size}")
            print(f"  Interval: {config.batch_interval}s")
            
            # 创建调度器
            scheduler = LODScheduler(stage, camera_path=config.camera_path, centralized_config=config.lod_config)
            scheduler.build_octree_from_tileset(config.tileset_path)
            
            # 配置分批显示
            scheduler.set_batch_display_config(
                enabled=config.enable_batch_display,
                batch_size=config.batch_size,
                interval=config.batch_interval
            )
            
            print(f"\nApplying configuration and updating LOD...")
            start_time = time.time()
            
            # 更新LOD
            update_tileset_lod_visibility_hierarchical(
                stage, region_bounds, scheduler, verbose=True, config=config
            )
            
            # 如果启用了分批显示，等待一段时间让分批显示完成
            if config.enable_batch_display:
                print(f"\nWaiting for batch display to complete...")
                max_wait_time = 5.0  # 最多等待5秒
                wait_start = time.time()
                
                while scheduler.batch_display_active and (time.time() - wait_start) < max_wait_time:
                    scheduler.update_batch_display()
                    time.sleep(0.05)  # 短暂等待
                
                if scheduler.batch_display_active:
                    print(f"⚠️  Batch display still active after {max_wait_time}s timeout")
                else:
                    elapsed = time.time() - start_time
                    print(f"✅ Batch display completed in {elapsed:.2f}s")
            else:
                elapsed = time.time() - start_time
                print(f"✅ Immediate display completed in {elapsed:.2f}s")
            
            print(f"\nTest {i+1} completed. Press Enter to continue to next test...")
            if i < len(test_configs) - 1:  # 不是最后一个测试
                input()  # 等待用户按Enter
        
        print("\n" + "="*60)
        print("✅ All batch display tests completed!")
        print("="*60)
        print("💡 Observations:")
        print("   1. Disabled batch display shows all tiles immediately")
        print("   2. Enabled batch display shows tiles in sequential batches")
        print("   3. Smaller batch sizes provide more granular control")
        print("   4. Longer intervals provide more visible separation between batches")
        print("   5. Watch the console output to see the batch progression")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def quick_enable_batch_display():
    """快速启用分批显示"""
    print("=== Quick Enable Batch Display ===")
    
    try:
        from simple_tileset_lod_example_standalone import (
            TilesetConfig,
            get_tileset_region_bounds_from_stage,
            update_tileset_lod_visibility_hierarchical
        )
        from lod_scheduler import LODScheduler
        import omni.usd
        
        stage = omni.usd.get_context().get_stage()
        if not stage:
            print("❌ No stage available")
            return False
        
        region_bounds = get_tileset_region_bounds_from_stage()
        if not region_bounds:
            print("❌ No tileset region bounds found")
            return False
        
        config = TilesetConfig()
        config.enable_batch_display = True
        config.batch_size = 2
        config.batch_interval = 0.2
        
        scheduler = LODScheduler(stage, camera_path=config.camera_path, centralized_config=config.lod_config)
        scheduler.build_octree_from_tileset(config.tileset_path)
        
        scheduler.set_batch_display_config(
            enabled=config.enable_batch_display,
            batch_size=config.batch_size,
            interval=config.batch_interval
        )
        
        print("🔧 Enabling batch display...")
        print(f"   Batch size: {config.batch_size}")
        print(f"   Interval: {config.batch_interval}s")
        
        update_tileset_lod_visibility_hierarchical(
            stage, region_bounds, scheduler, verbose=True, config=config
        )
        
        print("✅ Batch display enabled!")
        print("💡 Tiles should now display in sequential batches")
        print("💡 Watch the console for batch progression messages")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to enable batch display: {e}")
        return False

def monitor_batch_display():
    """监控分批显示进度"""
    print("=== Monitoring Batch Display ===")
    
    try:
        from simple_tileset_lod_example_standalone import TilesetConfig
        from lod_scheduler import LODScheduler
        import omni.usd
        
        stage = omni.usd.get_context().get_stage()
        if not stage:
            print("❌ No stage available")
            return False
        
        config = TilesetConfig()
        scheduler = LODScheduler(stage, camera_path=config.camera_path, centralized_config=config.lod_config)
        
        print(f"Batch display status:")
        print(f"  Active: {scheduler.batch_display_active}")
        print(f"  Queue length: {len(scheduler.current_batch_queue)}")
        print(f"  Batch size: {scheduler.batch_size}")
        print(f"  Interval: {scheduler.batch_interval}s")
        
        if scheduler.batch_display_active:
            print(f"\n🔄 Batch display is active, monitoring progress...")
            start_time = time.time()
            
            while scheduler.batch_display_active and (time.time() - start_time) < 10.0:
                scheduler.update_batch_display()
                print(f"  Queue remaining: {len(scheduler.current_batch_queue)}")
                time.sleep(0.1)
            
            if scheduler.batch_display_active:
                print(f"⚠️  Batch display still active after 10s")
            else:
                print(f"✅ Batch display completed")
        else:
            print(f"ℹ️  No active batch display")
        
        return True
        
    except Exception as e:
        print(f"❌ Monitoring failed: {e}")
        return False

# 便捷函数
def test():
    """运行完整测试"""
    return test_batch_display_in_script_editor()

def enable():
    """快速启用分批显示"""
    return quick_enable_batch_display()

def monitor():
    """监控分批显示"""
    return monitor_batch_display()

if __name__ == "__main__":
    print("Tile Batch Display Test Script")
    print("Available functions:")
    print("  - test()     # Run complete batch display test")
    print("  - enable()   # Quick enable batch display")
    print("  - monitor()  # Monitor batch display progress")
    print("\nTo run in Script Editor, call any of these functions.")
