# 瓦片深度排序和分批显示解决指南

## 问题描述

在LOD瓦片系统中，您遇到了两个主要问题：
1. **深度排序问题**：同一depth下远处的tile比近处的tile先显示，导致遮挡关系不正确
2. **渲染顺序问题**：即使排序了，渲染器可能不会按照期望的顺序处理同时设置为可见的瓦片

## 问题原因

1. **瓦片遍历顺序不当**：原始代码按照USD场景图的结构顺序遍历瓦片
2. **缺乏深度排序**：没有根据瓦片到相机的距离进行排序
3. **同帧渲染问题**：所有瓦片在同一帧内同时设置为可见，渲染器可能不按预期顺序处理

## 解决方案

### 1. 深度排序功能

#### 距离计算函数
```python
def calculate_tile_distance_to_camera(tile, camera_position):
    """计算瓦片到相机的距离"""
    center = tile['bounds'].center
    distance = math.sqrt(
        (camera_position[0] - center[0])**2 +
        (camera_position[1] - center[1])**2 +
        (camera_position[2] - center[2])**2
    )
    return distance
```

#### 可配置的排序策略
```python
# 在 TilesetConfig 中
self.enable_depth_sorting = True
self.depth_sorting_strategy = "far_to_near"  # 推荐设置
```

### 2. 分批显示功能

为了确保瓦片按正确顺序渲染，我们实现了分批显示机制：

#### 核心特性
- **分批处理**：瓦片按批次在不同帧中显示
- **可配置参数**：批次大小和间隔可调
- **顺序保证**：确保远处瓦片先显示，近处瓦片后显示

#### 配置选项
```python
# 在 TilesetConfig 中
self.enable_batch_display = True  # 启用分批显示
self.batch_size = 3  # 每批显示的瓦片数量
self.batch_interval = 0.15  # 批次间隔（秒）
```

#### 实现机制
```python
def _show_lod_tiles_for_level(self, lod_tiles, target_level):
    """支持分批显示的瓦片显示函数"""
    # 1. 排序瓦片
    sorted_tiles = sorted(lod_tiles, key=lambda t: (t.depth, t.tile_index))
    
    # 2. 过滤需要显示的瓦片
    tiles_to_show = [tile for tile in sorted_tiles if tile.lod_level == target_level]
    
    # 3. 分批显示或立即显示
    if self.batch_display_enabled and len(tiles_to_show) > 1:
        self._start_batch_display(tiles_to_show)
    else:
        # 立即显示所有瓦片
        for tile in tiles_to_show:
            self._set_tile_visibility(tile, True)
```

### 3. 主循环集成

分批显示需要在主循环中持续更新：

```python
def on_update(_):
    # 更新分批显示进度
    scheduler.update_batch_display()
    
    # 其他LOD更新逻辑...
```

## 使用方法

### 方法1：使用测试脚本

```python
# 测试深度排序
from lod.test_depth_sorting import test, fix
test()  # 完整测试
fix()   # 快速修复

# 测试分批显示
from lod.test_batch_display import test, enable, monitor
test()     # 完整测试
enable()   # 快速启用
monitor()  # 监控进度
```

### 方法2：配置参数

```python
config = TilesetConfig()

# 深度排序配置
config.enable_depth_sorting = True
config.depth_sorting_strategy = "far_to_near"

# 分批显示配置
config.enable_batch_display = True
config.batch_size = 2
config.batch_interval = 0.2
```

### 方法3：手动配置调度器

```python
scheduler = LODScheduler(stage, camera_path="/World/Camera", centralized_config=config.lod_config)

# 配置分批显示
scheduler.set_batch_display_config(
    enabled=True,
    batch_size=3,
    interval=0.15
)
```

## 参数调优指南

### 深度排序策略
- **`"far_to_near"`** (推荐): 远处瓦片先渲染，确保正确遮挡关系
- **`"near_to_far"`**: 近处瓦片先渲染
- **`"none"`**: 只按depth排序，不考虑距离

### 分批显示参数
- **batch_size**: 
  - 较小值(1-2): 更精确的顺序控制，但显示较慢
  - 较大值(3-5): 更快的显示速度，但顺序控制较粗糙
- **batch_interval**:
  - 较短间隔(0.05-0.1s): 快速显示
  - 较长间隔(0.2-0.5s): 明显的分批效果，便于观察

## 验证效果

### 1. 控制台输出
启用后，您会看到类似输出：
```
🔄 Starting batch display for 6 tiles (batch size: 2)
✅ Showing tile: /World/TilesetRegion/Tile_0_0/Content_LOD_20 (depth: 0, index: 0)
✅ Showing tile: /World/TilesetRegion/Tile_0_1/Content_LOD_20 (depth: 0, index: 1)
📦 Displayed batch of 2 tiles, 4 remaining
```

### 2. 视觉验证
- 观察瓦片按批次逐步出现
- 远处瓦片先显示，近处瓦片后显示
- 正确的遮挡关系

### 3. 性能监控
```python
# 监控分批显示状态
from lod.test_batch_display import monitor
monitor()
```

## 故障排除

### 问题1：分批显示不工作
- 检查 `enable_batch_display` 是否为 `True`
- 确保在主循环中调用 `scheduler.update_batch_display()`
- 验证瓦片数量是否大于1

### 问题2：显示顺序仍然不正确
- 检查瓦片边界框信息是否正确
- 验证相机位置计算
- 尝试调整 `batch_size` 为1以获得最精确的控制

### 问题3：显示太慢
- 减小 `batch_interval`
- 增大 `batch_size`
- 考虑禁用分批显示以获得最快速度

## 技术细节

### 分批显示算法
1. **排序**：按 (depth, -distance) 排序瓦片
2. **分批**：将瓦片分成固定大小的批次
3. **定时显示**：按间隔时间逐批显示
4. **状态跟踪**：维护显示队列和进度状态

### 性能考虑
- 分批显示会增加总显示时间
- 适合需要精确顺序控制的场景
- 对于性能敏感的应用，可以禁用分批显示

## 相关文件

- `lod_scheduler.py`: 分批显示核心实现
- `simple_tileset_lod_example_standalone.py`: 深度排序和主循环集成
- `test_depth_sorting.py`: 深度排序测试脚本
- `test_batch_display.py`: 分批显示测试脚本

## 总结

通过结合深度排序和分批显示功能，我们解决了瓦片显示顺序问题：

1. **深度排序**确保瓦片按正确的空间关系排序
2. **分批显示**确保渲染器按预期顺序处理瓦片
3. **可配置参数**允许根据具体需求调优性能和效果

推荐配置：
- `depth_sorting_strategy = "far_to_near"`
- `enable_batch_display = True`
- `batch_size = 2-3`
- `batch_interval = 0.1-0.2s`
