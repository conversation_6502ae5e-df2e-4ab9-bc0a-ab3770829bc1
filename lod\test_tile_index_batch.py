"""
测试tile_index分批显示功能
验证同一LOD级别下的瓦片按tile_index从小到大分批显示
"""

import sys
import os
import time

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

def test_tile_index_batch_display():
    """测试tile_index分批显示功能"""
    print("=== Tile Index Batch Display Test ===")
    
    try:
        # 导入必要的模块
        from simple_tileset_lod_example_standalone import (
            TilesetConfig,
            get_tileset_region_bounds_from_stage,
            collect_all_tiles_from_stage,
            update_tileset_lod_visibility_hierarchical
        )
        from lod_scheduler import LODScheduler
        import omni.usd
        
        # 获取当前stage
        stage = omni.usd.get_context().get_stage()
        if not stage:
            print("❌ ERROR: No stage available. Please load a USD scene first.")
            return False
        
        print("✅ Stage found")
        
        # 获取tileset区域边界
        region_bounds = get_tileset_region_bounds_from_stage()
        if not region_bounds:
            print("❌ ERROR: Could not find tileset region bounds in stage.")
            return False
        
        print("✅ Tileset region bounds found")
        
        # 分析当前瓦片结构
        all_tiles = collect_all_tiles_from_stage()
        if not all_tiles:
            print("❌ ERROR: No tiles found in stage.")
            return False
        
        print(f"✅ Found {len(all_tiles)} tiles in stage")
        
        # 分析瓦片的depth和tile_index分布
        depth_analysis = {}
        for tile in all_tiles:
            depth = tile.get('depth', 0)
            if depth not in depth_analysis:
                depth_analysis[depth] = []
            
            for content in tile.get('content_nodes', []):
                lod_level = content.get('lod_level', 'Unknown')
                depth_analysis[depth].append({
                    'tile_name': tile['name'],
                    'lod_level': lod_level,
                    'tile_index': getattr(tile, 'tile_index', 'Unknown')
                })
        
        print(f"\n📊 Tile Structure Analysis:")
        for depth in sorted(depth_analysis.keys()):
            tiles_at_depth = depth_analysis[depth]
            print(f"  Depth {depth}: {len(tiles_at_depth)} content nodes")
            
            # 按LOD级别分组
            lod_groups = {}
            for tile_info in tiles_at_depth:
                lod = tile_info['lod_level']
                if lod not in lod_groups:
                    lod_groups[lod] = []
                lod_groups[lod].append(tile_info)
            
            for lod_level in sorted(lod_groups.keys()):
                tiles_in_lod = lod_groups[lod_level]
                tile_indices = [str(t['tile_index']) for t in tiles_in_lod]
                print(f"    LOD {lod_level}: {len(tiles_in_lod)} tiles, indices: [{', '.join(tile_indices)}]")
        
        # 创建配置并测试分批显示
        config = TilesetConfig()
        
        # 测试不同的分批配置
        test_configs = [
            {"batch_size": 1, "interval": 0.3, "name": "One tile per batch (slow)"},
            {"batch_size": 2, "interval": 0.2, "name": "Two tiles per batch"},
            {"batch_size": 3, "interval": 0.15, "name": "Three tiles per batch"},
        ]
        
        for i, test_config in enumerate(test_configs):
            print(f"\n" + "="*70)
            print(f"TEST {i+1}: {test_config['name']}")
            print("="*70)
            
            # 配置分批显示
            config.enable_batch_display = True
            config.batch_size = test_config["batch_size"]
            config.batch_interval = test_config["interval"]
            
            print(f"Configuration:")
            print(f"  Batch size: {config.batch_size}")
            print(f"  Interval: {config.batch_interval}s")
            
            # 创建调度器
            scheduler = LODScheduler(stage, camera_path=config.camera_path, centralized_config=config.lod_config)
            scheduler.build_octree_from_tileset(config.tileset_path)
            
            # 配置分批显示
            scheduler.set_batch_display_config(
                enabled=config.enable_batch_display,
                batch_size=config.batch_size,
                interval=config.batch_interval
            )
            
            print(f"\n🔄 Applying LOD update with batch display...")
            start_time = time.time()
            
            # 更新LOD
            update_tileset_lod_visibility_hierarchical(
                stage, region_bounds, scheduler, verbose=True, config=config
            )
            
            # 等待分批显示完成
            print(f"\n⏳ Waiting for batch display to complete...")
            max_wait_time = 10.0  # 最多等待10秒
            wait_start = time.time()
            
            while scheduler.batch_display_active and (time.time() - wait_start) < max_wait_time:
                scheduler.update_batch_display()
                time.sleep(0.05)  # 短暂等待
            
            elapsed = time.time() - start_time
            
            if scheduler.batch_display_active:
                print(f"⚠️  Batch display still active after {max_wait_time}s timeout")
            else:
                print(f"✅ Batch display completed in {elapsed:.2f}s")
            
            print(f"\n💡 Expected behavior:")
            print(f"   - Tiles should appear in tile_index order (0, 1, 2, ...)")
            print(f"   - Each batch should show {config.batch_size} tiles")
            print(f"   - Interval between batches: {config.batch_interval}s")
            
            if i < len(test_configs) - 1:  # 不是最后一个测试
                print(f"\nPress Enter to continue to next test...")
                input()
        
        print("\n" + "="*70)
        print("✅ Tile Index Batch Display Test Completed!")
        print("="*70)
        print("💡 Key Points:")
        print("   1. Watch console output for tile_index order")
        print("   2. Tiles should appear in ascending tile_index order")
        print("   3. Each batch shows the configured number of tiles")
        print("   4. Smaller batch sizes provide more granular control")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def quick_test_tile_index_order():
    """快速测试tile_index顺序"""
    print("=== Quick Tile Index Order Test ===")
    
    try:
        from simple_tileset_lod_example_standalone import (
            TilesetConfig,
            get_tileset_region_bounds_from_stage,
            update_tileset_lod_visibility_hierarchical
        )
        from lod_scheduler import LODScheduler
        import omni.usd
        
        stage = omni.usd.get_context().get_stage()
        if not stage:
            print("❌ No stage available")
            return False
        
        region_bounds = get_tileset_region_bounds_from_stage()
        if not region_bounds:
            print("❌ No tileset region bounds found")
            return False
        
        config = TilesetConfig()
        config.enable_batch_display = True
        config.batch_size = 1  # 一次显示一个瓦片，最清楚地看到顺序
        config.batch_interval = 0.5  # 较长间隔，便于观察
        
        scheduler = LODScheduler(stage, camera_path=config.camera_path, centralized_config=config.lod_config)
        scheduler.build_octree_from_tileset(config.tileset_path)
        
        scheduler.set_batch_display_config(
            enabled=config.enable_batch_display,
            batch_size=config.batch_size,
            interval=config.batch_interval
        )
        
        print("🔧 Testing tile_index order with batch_size=1, interval=0.5s")
        print("💡 Watch console output to see tile_index progression...")
        
        update_tileset_lod_visibility_hierarchical(
            stage, region_bounds, scheduler, verbose=True, config=config
        )
        
        # 监控分批显示进度
        print("\n⏳ Monitoring batch display progress...")
        start_time = time.time()
        
        while scheduler.batch_display_active and (time.time() - start_time) < 15.0:
            scheduler.update_batch_display()
            time.sleep(0.1)
        
        if scheduler.batch_display_active:
            print(f"⚠️  Batch display still active after 15s")
        else:
            print(f"✅ Batch display completed!")
        
        print("💡 Expected: tile_index should appear in order: 0, 1, 2, 3, ...")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

# 便捷函数
def test():
    """运行完整测试"""
    return test_tile_index_batch_display()

def quick():
    """快速测试"""
    return quick_test_tile_index_order()

if __name__ == "__main__":
    print("Tile Index Batch Display Test Script")
    print("Available functions:")
    print("  - test()   # Run complete tile index batch test")
    print("  - quick()  # Quick tile index order test")
    print("\nTo run in Script Editor, call any of these functions.")
