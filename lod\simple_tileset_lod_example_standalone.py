"""
简单Tileset LOD示例 - Standalone版本
基于tileset.json文件构建八叉树并进行LOD管理
支持Isaac Sim SimulationApp和直接运行
"""

import asyncio
import time
import math
import sys
import os
import json

# Isaac Sim standalone 支持
from isaacsim import SimulationApp
# HUD 配置标志位
DISP_FPS        = 1 << 0
DISP_RESOLUTION = 1 << 3
DISP_DEV_MEM    = 1 << 13
DISP_HOST_MEM   = 1 << 14

config = {
    "width": 1280,
    "height": 720,
    "headless": False,
    "display_options": DISP_FPS | DISP_RESOLUTION | DISP_DEV_MEM | DISP_HOST_MEM,
}

simulation_app = SimulationApp(launch_config=config)

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from pxr import UsdGeom, Gf
import omni.usd
import omni.kit.app
import omni.timeline
import omni.kit.commands
import numpy as np

# 导入LOD调度器
try:
    from lod_scheduler import LODScheduler, BoundingBox, LODLevel, LODTile
    print("✓ Successfully imported lod_scheduler")
except ImportError as e:
    print(f"⚠️ Warning: Could not import lod_scheduler: {e}")
    print("Will use built-in LOD classes instead")
    sys.exit(1)

# 配置类
class TilesetConfig:
    """Tileset LOD配置类"""
    def __init__(self):
        self.tileset_path = "E:/wanleqi/isaacsim-python-scripts/lod/tileset_data/florenz_village/hierarchy/tileset_hierarchy.json"  # 默认路径，需要用户修改

        self.usd_file_path = "C:/test-usd-path"  # 默认路径，需要用户修改

        # 相机配置
        self.camera_path = "/World/Camera"

        # LOD切换配置（SSE与距离阈值）
        self.auto_mode = "movement"  # "movement" 或 "timer"
        self.timer_interval = 1.0  # 主线程事件订阅间隔（秒）
        self.debug_info = True  # 是否输出调试信息

        # 运行时控制
        self.auto_start_runtime = True  # 是否自动开启运行时

        # 相机移动模拟配置
        self.camera_start_position = Gf.Vec3f(0, 0, 50)  # 相机起始位置
        self.camera_target_position = Gf.Vec3f(20, 0, 100)  # 相机目标位置
        self.camera_movement_duration = 30.0  # 移动持续时间（秒）
        self.camera_movement_loop = True  # 是否循环移动

        # 分批显示配置
        self.enable_batch_display = False  # 是否启用分批显示
        self.batch_size = 3  # 每批显示的瓦片数量
        self.batch_interval = 0.15  # 批次间隔（秒）

        # 使用中心化LOD配置
        from lod_config import create_lod_config_from_tileset
        self.lod_config = create_lod_config_from_tileset(self.tileset_path)

        # 从中心化配置获取SSE配置
        self.maximum_screen_space_error = self.lod_config.maximum_screen_space_error
        self.screen_width = self.lod_config.screen_width
        self.horizontal_fov = self.lod_config.horizontal_fov

# 全局变量来跟踪当前LOD状态
current_lod_state = {"last_lod": None, "last_distance": None}
# 全局变量来跟踪实时更新状态
realtime_update_active = False
realtime_update_data = None
# 全局变量来跟踪自动更新订阅
auto_update_subscription = None

def get_tileset_region_bounds_from_stage():
    """从已加载的stage中获取tileset区域边界"""
    try:
        stage = omni.usd.get_context().get_stage()
        if not stage:
            print("ERROR: No stage available")
            return None

        # 查找TilesetRegion
        region_path = "/World/TilesetRegion"
        region_prim = stage.GetPrimAtPath(region_path)

        if not region_prim or not region_prim.IsValid():
            print(f"ERROR: TilesetRegion not found at {region_path}")
            return None

        # 递归查找第一个有边界信息的tile
        def find_bounds_in_hierarchy(prim):
            """递归查找有边界信息的tile"""
            # 检查当前prim是否有边界信息
            min_bounds_attr = prim.GetAttribute("tileset:minBounds")
            max_bounds_attr = prim.GetAttribute("tileset:maxBounds")

            if min_bounds_attr and max_bounds_attr:
                min_point = Gf.Vec3f(*min_bounds_attr.Get())
                max_point = Gf.Vec3f(*max_bounds_attr.Get())
                return BoundingBox(min_point, max_point)

            # 递归检查子节点
            for child in prim.GetChildren():
                bounds = find_bounds_in_hierarchy(child)
                if bounds:
                    return bounds

            return None

        # 从根tile开始查找边界
        bounds = find_bounds_in_hierarchy(region_prim)

        if bounds:
            print(f"Found tileset region bounds: {bounds.min_point} to {bounds.max_point}")
            print(f"Region center: {bounds.center}")
            print(f"Region size: {bounds.size}")
            return bounds
        else:
            print("ERROR: No tile with bounds found in TilesetRegion hierarchy")
            return None

    except Exception as e:
        print(f"ERROR: Failed to get tileset region bounds: {e}")
        return None

def collect_all_tiles_from_stage():
    """从stage中收集所有的tile节点信息"""
    try:
        stage = omni.usd.get_context().get_stage()
        if not stage:
            print("ERROR: No stage available")
            return []

        # 查找TilesetRegion
        region_path = "/World/TilesetRegion"
        region_prim = stage.GetPrimAtPath(region_path)

        if not region_prim or not region_prim.IsValid():
            print(f"ERROR: TilesetRegion not found at {region_path}")
            return []

        tiles = []

        def collect_tiles_recursive(prim, path=""):
            """递归收集tile信息"""
            prim_name = prim.GetName()
            current_path = f"{path}/{prim_name}" if path else prim_name

            # 检查是否是tile节点
            if prim_name.startswith("Tile_"):
                tile_info = {
                    'path': prim.GetPath().pathString,
                    'name': prim_name,
                    'prim': prim,
                    'content_nodes': [],
                    'depth': None,
                    'geometric_error': None,
                    'has_content': False,
                    'bounds': None
                }

                # 获取tile属性
                depth_attr = prim.GetAttribute("tileset:depth")
                if depth_attr:
                    tile_info['depth'] = depth_attr.Get()

                geom_error_attr = prim.GetAttribute("tileset:geometricError")
                if geom_error_attr:
                    tile_info['geometric_error'] = geom_error_attr.Get()

                has_content_attr = prim.GetAttribute("tileset:hasContent")
                if has_content_attr:
                    tile_info['has_content'] = has_content_attr.Get()

                # 获取边界
                min_bounds_attr = prim.GetAttribute("tileset:minBounds")
                max_bounds_attr = prim.GetAttribute("tileset:maxBounds")
                if min_bounds_attr and max_bounds_attr:
                    min_point = Gf.Vec3f(*min_bounds_attr.Get())
                    max_point = Gf.Vec3f(*max_bounds_attr.Get())
                    tile_info['bounds'] = BoundingBox(min_point, max_point)

                # 查找内容节点
                for child in prim.GetChildren():
                    child_name = child.GetName()
                    if child_name.startswith("Content_LOD_"):
                        content_info = {
                            'path': child.GetPath().pathString,
                            'name': child_name,
                            'prim': child,
                            'lod_level': None,
                            'uri': None
                        }

                        # 获取LOD级别
                        lod_level_attr = child.GetAttribute("tileset:lodLevel")
                        if lod_level_attr:
                            content_info['lod_level'] = lod_level_attr.Get()

                        # 获取URI
                        uri_attr = child.GetAttribute("tileset:uri")
                        if uri_attr:
                            content_info['uri'] = uri_attr.Get()

                        tile_info['content_nodes'].append(content_info)

                tiles.append(tile_info)

            # 递归处理子节点
            for child in prim.GetChildren():
                collect_tiles_recursive(child, current_path)

        # 从TilesetRegion开始收集
        collect_tiles_recursive(region_prim)

        print(f"Collected {len(tiles)} tiles from stage hierarchy")
        return tiles

    except Exception as e:
        print(f"ERROR: Failed to collect tiles from stage: {e}")
        return []

def extract_camera_frustum_planes(camera_prim):
    """从相机 prim 中提取视锥体平面"""
    try:
        # 获取相机的变换矩阵
        xformable = UsdGeom.Xformable(camera_prim)
        transform_matrix = xformable.GetLocalTransformation()

        # 获取相机属性
        camera = UsdGeom.Camera(camera_prim)

        # 获取视野角度（水平FOV）
        horizontal_aperture = camera.GetHorizontalApertureAttr().Get() or 20.955  # 默认值
        vertical_aperture = camera.GetVerticalApertureAttr().Get() or 15.2908    # 默认值
        focal_length = camera.GetFocalLengthAttr().Get() or 50.0                 # 默认值

        # 计算视野角度（弧度）
        h_fov = 2.0 * math.atan(horizontal_aperture / (2.0 * focal_length))
        v_fov = 2.0 * math.atan(vertical_aperture / (2.0 * focal_length))

        # 获取近远平面距离
        near_distance = camera.GetClippingRangeAttr().Get()[0] if camera.GetClippingRangeAttr().Get() else 0.1
        far_distance = camera.GetClippingRangeAttr().Get()[1] if camera.GetClippingRangeAttr().Get() else 1000.0

        # 提取相机位置和方向
        camera_pos = Gf.Vec3f(transform_matrix.ExtractTranslation())

        # 提取旋转矩阵并计算方向向量
        rotation_matrix = transform_matrix.ExtractRotationMatrix()

        # USD 相机默认朝向 -Z 方向
        forward = -Gf.Vec3f(rotation_matrix[2][0], rotation_matrix[2][1], rotation_matrix[2][2])
        up = Gf.Vec3f(rotation_matrix[1][0], rotation_matrix[1][1], rotation_matrix[1][2])
        right = Gf.Vec3f(rotation_matrix[0][0], rotation_matrix[0][1], rotation_matrix[0][2])

        # 计算视锥体的6个平面（法向量指向内部）
        planes = []

        # 近平面
        near_center = camera_pos + forward * near_distance
        planes.append((forward, near_center))

        # 远平面
        far_center = camera_pos + forward * far_distance
        planes.append((-forward, far_center))

        # 左平面
        half_h_fov = h_fov * 0.5
        left_normal = math.cos(half_h_fov) * right + math.sin(half_h_fov) * forward
        left_normal = left_normal.GetNormalized()
        planes.append((left_normal, camera_pos))

        # 右平面
        right_normal = -math.cos(half_h_fov) * right + math.sin(half_h_fov) * forward
        right_normal = right_normal.GetNormalized()
        planes.append((right_normal, camera_pos))

        # 上平面
        half_v_fov = v_fov * 0.5
        top_normal = -math.cos(half_v_fov) * up + math.sin(half_v_fov) * forward
        top_normal = top_normal.GetNormalized()
        planes.append((top_normal, camera_pos))

        # 下平面
        bottom_normal = math.cos(half_v_fov) * up + math.sin(half_v_fov) * forward
        bottom_normal = bottom_normal.GetNormalized()
        planes.append((bottom_normal, camera_pos))

        return planes, camera_pos, forward

    except Exception as e:
        print(f"Error extracting camera frustum: {e}")
        return None, None, None

def is_bounding_box_in_frustum(bbox, frustum_planes):
    """检查边界框是否与视锥体相交"""
    if not bbox or not frustum_planes:
        return True  # 如果无法检测，默认可见

    try:
        # 获取边界框的8个顶点
        min_pt = bbox.min_point
        max_pt = bbox.max_point

        vertices = [
            Gf.Vec3f(min_pt[0], min_pt[1], min_pt[2]),
            Gf.Vec3f(max_pt[0], min_pt[1], min_pt[2]),
            Gf.Vec3f(min_pt[0], max_pt[1], min_pt[2]),
            Gf.Vec3f(max_pt[0], max_pt[1], min_pt[2]),
            Gf.Vec3f(min_pt[0], min_pt[1], max_pt[2]),
            Gf.Vec3f(max_pt[0], min_pt[1], max_pt[2]),
            Gf.Vec3f(min_pt[0], max_pt[1], max_pt[2]),
            Gf.Vec3f(max_pt[0], max_pt[1], max_pt[2])
        ]

        # 对每个平面进行测试
        for plane_normal, plane_point in frustum_planes:
            # 检查所有顶点是否都在平面的外侧
            all_outside = True
            for vertex in vertices:
                # 计算点到平面的距离
                to_vertex = vertex - plane_point
                distance = Gf.Dot(to_vertex, plane_normal)

                if distance >= 0:  # 顶点在平面内侧或平面上
                    all_outside = False
                    break

            # 如果所有顶点都在某个平面外侧，则边界框完全在视锥体外
            if all_outside:
                return False

        # 如果没有被任何平面完全排除，则认为相交
        return True

    except Exception as e:
        print(f"Error checking bounding box in frustum: {e}")
        return True  # 出错时默认可见

def _display_tiles_in_batches(visible_items, batch_size, batch_interval, verbose=True):
    """按批次显示瓦片，确保按tile_index顺序显示"""
    import time

    if not visible_items:
        return

    # 将瓦片分成批次
    batches = []
    for i in range(0, len(visible_items), batch_size):
        batch = visible_items[i:i + batch_size]
        batches.append(batch)

    if verbose:
        print(f"📦 Divided into {len(batches)} batches")

    # 逐批显示
    for batch_idx, batch in enumerate(batches):
        if verbose:
            tile_indices = [str(item['tile_index']) for item in batch]
            print(f"📦 Batch {batch_idx + 1}/{len(batches)}: Showing tiles with tile_index [{', '.join(tile_indices)}]")

        # 显示当前批次的瓦片
        for item in batch:
            content = item['content']
            content_prim = content['prim']
            imageable = UsdGeom.Imageable(content_prim)
            vis_attr = imageable.GetVisibilityAttr()
            vis_attr.Set(UsdGeom.Tokens.inherited)

            if verbose:
                print(f"  ✅ Showing tile: {content['name']} (tile_index: {item['tile_index']}) at {item['tile']['name']}")

        # 如果不是最后一批，等待指定间隔
        if batch_idx < len(batches) - 1:
            if verbose:
                print(f"⏳ Waiting {batch_interval}s before next batch...")
            time.sleep(batch_interval)

    if verbose:
        print(f"✅ Batch display completed! All {len(visible_items)} tiles are now visible.")

def start_runtime():
    """启动运行时（播放模式）"""
    try:
        timeline = omni.timeline.get_timeline_interface()
        timeline.play()
        print("Runtime started (Play mode)")
        return True
    except Exception as e:
        print(f"ERROR: Failed to start runtime: {e}")
        return False

def stop_runtime():
    """停止运行时"""
    try:
        timeline = omni.timeline.get_timeline_interface()
        timeline.stop()
        print("Runtime stopped")
        return True
    except Exception as e:
        print(f"ERROR: Failed to stop runtime: {e}")
        return False

def update_tileset_lod_visibility_hierarchical(stage, region_bounds, scheduler, verbose=True, config=None):
    """使用调度器更新分层tileset LOD可见性 - 支持精确LOD控制、视锥体检测和分批显示"""
    if verbose:
        print("\n=== Updating Hierarchical Tileset LOD Visibility with Frustum Culling ===")

    # 检查是否启用分批显示
    enable_batch_display = False
    batch_size = 3
    batch_interval = 0.15

    if config:
        enable_batch_display = getattr(config, 'enable_batch_display', False)
        batch_size = getattr(config, 'batch_size', 3)
        batch_interval = getattr(config, 'batch_interval', 0.15)

    try:
        # 获取相机
        camera = stage.GetPrimAtPath(scheduler.camera_path)
        if not camera:
            if verbose:
                print("ERROR: Camera not found")
            return None, None, None

        # 获取相机位置和视锥体
        xformable = UsdGeom.Xformable(camera)
        transform = xformable.GetLocalTransformation()
        camera_position = Gf.Vec3f(transform.ExtractTranslation())

        # 提取视锥体平面
        frustum_planes, cam_pos, cam_forward = extract_camera_frustum_planes(camera)
        if not frustum_planes:
            if verbose:
                print("WARNING: Failed to extract camera frustum, disabling frustum culling")

        # 收集所有tiles
        all_tiles = collect_all_tiles_from_stage()
        if not all_tiles:
            if verbose:
                print("ERROR: No tiles found in stage")
            return None, None, None

        # 计算到区域中心的距离
        center_distance = math.sqrt(
            (camera_position[0] - region_bounds.center[0])**2 +
            (camera_position[1] - region_bounds.center[1])**2 +
            (camera_position[2] - region_bounds.center[2])**2
        )

        # 使用调度器选择合适的LOD级别
        selected_lod_name, representative_sse = scheduler.select_lod_by_sse_and_distance(
            region_bounds, camera_position, verbose=False
        )

        if not selected_lod_name:
            if verbose:
                print("ERROR: Failed to select LOD level")
            return None, None, None

        # 将LOD名称转换为数值级别进行精确匹配
        lod_name_to_level = {"High": 20, "Medium": 18, "Low": 15}
        target_lod_level = lod_name_to_level.get(selected_lod_name, 15)

        visible_count = 0
        hidden_count = 0
        frustum_culled_count = 0

        # 收集需要处理的内容节点，并按tile_index排序
        content_items = []
        for tile in all_tiles:
            if not tile['content_nodes']:
                continue  # 跳过没有内容的容器节点

            # 获取tile_index属性
            tile_index = 0  # 默认值
            tile_prim = tile['prim']
            tile_index_attr = tile_prim.GetAttribute("tileset:tileIndex")
            if tile_index_attr:
                tile_index = tile_index_attr.Get() or 0

            for content in tile['content_nodes']:
                content_lod_level = content.get('lod_level', 15)

                # 精确LOD匹配：只显示目标LOD级别的内容
                lod_match = (content_lod_level == target_lod_level)

                # 视锥体检测
                frustum_visible = True
                if frustum_planes and tile.get('bounds'):
                    frustum_visible = is_bounding_box_in_frustum(tile['bounds'], frustum_planes)
                    if not frustum_visible:
                        frustum_culled_count += 1

                # 最终可见性：LOD匹配 AND 在视锥体内
                visible = lod_match and frustum_visible

                # 添加到处理列表
                content_items.append({
                    'content': content,
                    'tile': tile,
                    'tile_index': tile_index,
                    'lod_level': content_lod_level,
                    'visible': visible,
                    'lod_match': lod_match,
                    'frustum_visible': frustum_visible
                })

        # 按tile_index排序（同一LOD级别下按tile_index从小到大）
        content_items.sort(key=lambda x: (x['lod_level'], x['tile_index']))

        if verbose:
            print(f"\n📋 Content processing order (sorted by LOD level and tile_index):")
            for i, item in enumerate(content_items):
                if item['visible']:
                    print(f"  {i+1:2d}. tile_index={item['tile_index']:2d}, LOD={item['lod_level']:2d}, "
                          f"tile={item['tile']['name']}, content={item['content']['name']}")

        # 分离可见和不可见的内容
        visible_items = [item for item in content_items if item['visible']]
        hidden_items = [item for item in content_items if not item['visible']]

        # 先隐藏所有不可见的内容
        for item in hidden_items:
            content = item['content']
            content_prim = content['prim']
            imageable = UsdGeom.Imageable(content_prim)
            vis_attr = imageable.GetVisibilityAttr()
            vis_attr.Set(UsdGeom.Tokens.invisible)
            hidden_count += 1

            if verbose and item['lod_match'] and not item['frustum_visible']:
                print(f"  🚫 Frustum culled: {content['name']} (LOD {item['lod_level']}, tile_index {item['tile_index']}) at {item['tile']['name']}")

        # 处理可见内容：分批显示或立即显示
        if enable_batch_display and len(visible_items) > 1:
            if verbose:
                print(f"\n🔄 Starting batch display for {len(visible_items)} tiles (batch size: {batch_size}, interval: {batch_interval}s)")
                print(f"📋 Tiles will be displayed in tile_index order:")
                for i, item in enumerate(visible_items):
                    print(f"  {i+1:2d}. tile_index={item['tile_index']:2d}, {item['content']['name']} at {item['tile']['name']}")

            # 分批显示可见内容
            _display_tiles_in_batches(visible_items, batch_size, batch_interval, verbose)
            visible_count = len(visible_items)
        else:
            # 立即显示所有可见内容
            for item in visible_items:
                content = item['content']
                content_prim = content['prim']
                imageable = UsdGeom.Imageable(content_prim)
                vis_attr = imageable.GetVisibilityAttr()
                vis_attr.Set(UsdGeom.Tokens.inherited)
                visible_count += 1

                if verbose:
                    print(f"  ✅ Visible: {content['name']} (LOD {item['lod_level']}, tile_index {item['tile_index']}) at {item['tile']['name']}")

        if verbose:
            print(f"\nLOD Update Summary:")
            print(f"  Target LOD: {selected_lod_name} (level {target_lod_level})")
            print(f"  Camera distance: {center_distance:.1f}")
            print(f"  Screen space error: {representative_sse:.2f}")
            print(f"  Visible content nodes: {visible_count}")
            print(f"  Hidden content nodes: {hidden_count}")
            print(f"  Frustum culled nodes: {frustum_culled_count}")
            print(f"  Frustum culling: {'Enabled' if frustum_planes else 'Disabled'}")

        return selected_lod_name, center_distance, representative_sse

    except Exception as e:
        if verbose:
            print(f"Error updating hierarchical tileset LOD visibility: {e}")
            import traceback
            traceback.print_exc()
        return None, None, None

# 保持向后兼容性的别名
def update_tileset_lod_visibility(stage, region_bounds, scheduler, verbose=True):
    """向后兼容的LOD更新函数"""
    return update_tileset_lod_visibility_hierarchical(stage, region_bounds, scheduler, verbose)

def _start_mainthread_update_subscription(stage, region_bounds, scheduler, update_interval: float = 1.0):
    """使用 Kit 的 update 事件在主线程上进行节流更新"""
    global realtime_update_active, realtime_update_data, current_lod_state, auto_update_subscription

    # 防止重复订阅
    if auto_update_subscription:
        try:
            auto_update_subscription.unsubscribe()
        except Exception:
            pass
        auto_update_subscription = None

    import omni.kit.app
    app = omni.kit.app.get_app()
    stream = app.get_update_event_stream()

    last_ts = time.time()
    accumulator = 0.0

    def on_update(_):
        nonlocal last_ts, accumulator
        if not realtime_update_active:
            return

        now = time.time()
        dt = now - last_ts
        last_ts = now
        accumulator += dt

        if accumulator < update_interval:
            return
        accumulator = 0.0

        try:
            # 更新LOD并获取指标
            current_lod, distance, screen_error = update_tileset_lod_visibility(
                stage, region_bounds, scheduler, verbose=False
            )
            if (current_lod and
                (current_lod_state["last_lod"] != current_lod or
                abs((current_lod_state["last_distance"] or 0) - distance) > 10.0)):
                print(f"🔄 Tileset LOD switched to {current_lod} (distance: {distance}, screen: {screen_error})")
                current_lod_state["last_lod"] = current_lod
                current_lod_state["last_distance"] = distance
        except Exception as ex:
            print(f"Error in main-thread update: {ex}")

    auto_update_subscription = stream.create_subscription_to_pop(on_update, name="TilesetLODUpdateOnMainThread")

def start_automatic_tileset_lod_switching(config: 'TilesetConfig'):
    """启动自动tileset LOD切换 - 使用已配置的USD场景"""
    global realtime_update_active, realtime_update_data, auto_update_subscription

    print("\n=== Starting Automatic Tileset LOD Switching ===")

    # 获取当前stage
    stage = omni.usd.get_context().get_stage()
    if not stage:
        print("ERROR: No stage available. Please load a USD scene first.")
        return None, None, None

    # 从stage中获取tileset区域边界
    region_bounds = get_tileset_region_bounds_from_stage()
    if not region_bounds:
        print("ERROR: Could not find tileset region bounds in stage.")
        return None, None, None

    # 创建LOD调度器（传入中心化配置）
    scheduler = LODScheduler(stage, camera_path=config.camera_path, centralized_config=config.lod_config)

    # 注意：不需要再手动设置参数，构造函数已经从中心化配置中获取了所有参数

    # 从tileset构建八叉树
    scheduler.build_octree_from_tileset(config.tileset_path)

    # 保存数据到全局变量
    realtime_update_active = True

    # 启动主线程事件订阅
    try:
        print("Starting main-thread event-based automatic updates...")
        _start_mainthread_update_subscription(stage, region_bounds, scheduler, config.timer_interval)
        print("✅ Automatic tileset LOD switching started using main-thread event subscription!")
        print(f"The system will automatically update LOD every {config.timer_interval} seconds on the main thread.")
        return stage, region_bounds, scheduler
    except Exception as e:
        print(f"Main-thread event subscription failed: {e}")
        return stage, region_bounds, scheduler

def stop_automatic_tileset_lod_switching():
    """停止自动tileset LOD切换"""
    global realtime_update_active, realtime_update_data, auto_update_subscription

    print("\n=== Stopping Automatic Tileset LOD Switching ===")

    # 停止自动更新
    realtime_update_active = False
    realtime_update_data = None

    # 取消订阅
    if auto_update_subscription:
        try:
            auto_update_subscription.unsubscribe()
        except Exception:
            pass
        auto_update_subscription = None
    print("✅ Automatic tileset LOD switching stopped successfully!")

def check_current_tileset_lod_status(stage, region_bounds, scheduler):
    """检查当前分层tileset LOD状态"""
    print("\n=== Current Hierarchical Tileset LOD Status ===")

    try:
        # 获取相机位置
        camera = stage.GetPrimAtPath("/World/Camera")
        if not camera:
            print("Camera not found!")
            return

        xformable = UsdGeom.Xformable(camera)
        transform = xformable.GetLocalTransformation()
        camera_position = Gf.Vec3f(transform.ExtractTranslation())

        # 计算距离
        center = region_bounds.center
        distance = math.sqrt(
            (camera_position[0] - center[0])**2 +
            (camera_position[1] - center[1])**2 +
            (camera_position[2] - center[2])**2
        )

        # 使用调度器选择LOD
        selected_lod, _ = scheduler.select_lod_by_sse_and_distance(
            region_bounds, camera_position, verbose=True
        )

        print(f"Camera position: {camera_position}")
        print(f"Distance to region center: {distance:.1f}")
        print(f"Selected LOD level: {selected_lod}")

        # 收集并检查所有tiles的可见性状态
        all_tiles = collect_all_tiles_from_stage()

        print(f"\nTile hierarchy status:")
        print(f"Total tiles found: {len(all_tiles)}")

        visible_content = []
        hidden_content = []

        for tile in all_tiles:
            if not tile['content_nodes']:
                print(f"  📁 {tile['name']} (Container, depth {tile['depth']}, no content)")
                continue

            for content in tile['content_nodes']:
                content_prim = content['prim']
                imageable = UsdGeom.Imageable(content_prim)
                vis_attr = imageable.GetVisibilityAttr()
                visibility = vis_attr.Get()
                is_visible = (visibility == UsdGeom.Tokens.inherited)

                content_info = {
                    'tile_name': tile['name'],
                    'content_name': content['name'],
                    'lod_level': content.get('lod_level', 'Unknown'),
                    'uri': content.get('uri', 'Unknown'),
                    'depth': tile['depth']
                }

                if is_visible:
                    visible_content.append(content_info)
                else:
                    hidden_content.append(content_info)

        print(f"\nVisible content ({len(visible_content)}):")
        for content in visible_content:
            print(f"  ✅ {content['tile_name']}/{content['content_name']} (LOD {content['lod_level']}, depth {content['depth']})")

        print(f"\nHidden content ({len(hidden_content)}):")
        for content in hidden_content[:5]:  # 只显示前5个以避免输出过长
            print(f"  ❌ {content['tile_name']}/{content['content_name']} (LOD {content['lod_level']}, depth {content['depth']})")

        if len(hidden_content) > 5:
            print(f"  ... and {len(hidden_content) - 5} more hidden content nodes")

    except Exception as e:
        print(f"Error checking hierarchical tileset LOD status: {e}")
        import traceback
        traceback.print_exc()

def manual_tileset_lod_update(stage, region_bounds, scheduler):
    """手动更新tileset LOD（用于测试）"""
    print("\n=== Manual Tileset LOD Update ===")

    try:
        # 执行一次LOD更新
        current_lod, distance, _ = update_tileset_lod_visibility(
            stage, region_bounds, scheduler, verbose=True
        )

        if current_lod:
            # 更新状态跟踪
            global current_lod_state
            current_lod_state["last_lod"] = current_lod
            current_lod_state["last_distance"] = distance

            print(f"Manual update completed: {current_lod} LOD (distance: {distance:.1f})")
        else:
            print("Manual update failed")

    except Exception as e:
        print(f"Error in manual tileset LOD update: {e}")

# ============================================================================
# 主执行函数 - Standalone版本
# ============================================================================

def load_usd_stage(usd_file_path):
    """加载USD文件到stage"""
    try:
        print(f"Loading USD file: {usd_file_path}")

        # 检查文件是否存在
        import os
        if not os.path.exists(usd_file_path):
            print(f"ERROR: USD file not found: {usd_file_path}")
            return False

        # 使用与 auto_camera_optimizer_standalone.py 相同的方法打开stage
        print(f"Opening stage: '{usd_file_path}'")
        omni.usd.get_context().open_stage(usd_file_path)
        stage = omni.usd.get_context().get_stage()

        if not stage:
            print(f"ERROR: Failed to open USD file: {usd_file_path}")
            return False

        print(f"Successfully loaded USD file: {usd_file_path}")
        return True

    except Exception as e:
        print(f"ERROR: Failed to load USD file: {e}")
        return False

def wait_for_render_initialization(max_wait_time=10.0):
    """等待渲染系统初始化完成"""
    print("Waiting for render system initialization...")

    import time
    start_time = time.time()

    while time.time() - start_time < max_wait_time:
        # 更新 Isaac Sim 以推进渲染初始化
        simulation_app.update()

        # 检查是否有可用的 stage
        stage = omni.usd.get_context().get_stage()
        if stage:
            # 检查是否有 tileset prims（新的层次结构）
            tileset_prims = []
            for prim in stage.Traverse():
                # 检查新的tileset属性
                attr_depth = prim.GetAttribute("tileset:depth")
                attr_geom_error = prim.GetAttribute("tileset:geometricError")

                # 或者检查旧的nurec属性（向后兼容）
                attr_min = prim.GetAttribute("omni:nurec:crop:minBounds")
                attr_max = prim.GetAttribute("omni:nurec:crop:maxBounds")

                if (attr_depth and attr_geom_error) or (attr_min and attr_max):
                    tileset_prims.append(prim)

            if tileset_prims:
                print(f"Found {len(tileset_prims)} tileset prim(s), render system ready")
                return True
            else:
                print("Stage loaded but no tileset prims found, continuing to wait...")

        time.sleep(0.1)  # 短暂等待

    print(f"Warning: Render initialization timeout after {max_wait_time}s")
    return False

def run_tileset_standalone_mode(config):
    """运行tileset standalone模式 - 使用预配置的USD场景"""
    print("=== Simple Tileset LOD Example Standalone Mode ===")

    # 1. 检查tileset文件路径
    if not config.tileset_path or not os.path.exists(config.tileset_path):
        print(f"ERROR: Tileset file not found: {config.tileset_path}")
        return None

    # 2. 加载USD文件
    if not load_usd_stage(config.usd_file_path):
        return None

    # 3. 等待渲染系统初始化
    if not wait_for_render_initialization():
        print("WARNING: Render initialization may not be complete")

    # 3. 自动启动运行时（如果配置启用）
    if config.auto_start_runtime:
        if not start_runtime():
            print("WARNING: Failed to start runtime, continuing...")

    # 4. 启动自动tileset LOD切换
    print(f"\nStarting Automatic Tileset LOD Switching...")
    stage, region_bounds, scheduler = start_automatic_tileset_lod_switching(config)

    if stage and region_bounds and scheduler:
        print(f"\n=== Tileset standalone mode setup completed ===")
        print(f"USD file: {config.usd_file_path}")
        print(f"Tileset file: {config.tileset_path}")
        print(f"Camera path: {config.camera_path}")
        print(f"Auto mode: {config.auto_mode}")
        print(f"Timer interval: {config.timer_interval}s")
        print(f"Debug info: {config.debug_info}")

        return stage, region_bounds, scheduler
    else:
        print("ERROR: Failed to setup tileset standalone mode")
        return None

def main():
    """主函数 - Tileset Standalone版本，使用预配置的USD场景进行LOD切换"""
    print("Simple Tileset LOD Example - Standalone Version")
    print("=" * 60)
    print("This version uses pre-configured USD scenes created by tileset_usd_creator.py")
    print("The system will start automatic LOD switching immediately.")
    print("=" * 60)

    try:
        # 创建配置
        config = TilesetConfig()

        config.usd_file_path = "E:/wanleqi/LOD_Demo/lod-demo-tileset-complex.usd"

        # 直接启动tileset LOD切换
        print("\n🚀 Starting tileset LOD switching...")
        result = run_tileset_standalone_mode(config)

        if result:
            _, _, scheduler = result

            print("\n" + "=" * 50)
            print("✅ Tileset LOD switching started successfully!")
            print("\nFeatures:")
            print("- Uses pre-configured USD scene with tileset structure")
            print("- SSE (Screen Space Error) based LOD selection")
            print("- Automatic LOD switching based on camera distance")
            print("- Real-time updates as you move the camera")
            print("- Uses actual USDZ files with visibility control")
            print("\nThe system is now running automatically!")
            print("Move the camera in Omniverse to see LOD changes.")
            print("=" * 50)

            # 保持脚本运行（可选）
            print("\nPress Ctrl+C to stop...")
            try:
                print("Starting main loop...")
                while True:
                    simulation_app.update()  # 使用 simulation_app.update() 而不是 time.sleep()
                    time.sleep(0.01)  # 短暂延迟以避免过度占用CPU
            except KeyboardInterrupt:
                print("\nStopping...")
                if scheduler:
                    stop_automatic_tileset_lod_switching()
        else:
            print("❌ Failed to start tileset LOD switching")
            print("💡 Make sure you have created the USD scene using tileset_usd_creator.py first!")

    except Exception as e:
        print(f"❌ Error starting tileset LOD switching: {e}")
        import traceback
        traceback.print_exc()

# 便捷函数
def start_tileset_lod():
    """便捷函数：启动tileset LOD切换"""
    config = TilesetConfig()
    return run_tileset_standalone_mode(config)

def stop_tileset_lod():
    """便捷函数：停止tileset LOD切换"""
    return stop_automatic_tileset_lod_switching()

# 使用说明
print("\n" + "=" * 60)
print("Simple Tileset LOD Example - Standalone Version")
print("=" * 60)
print("\nThis script uses pre-configured USD scenes for dynamic LOD switching.")
print("It requires USD scenes created by tileset_usd_creator.py.")
print("\nFeatures:")
print("- Uses pre-configured USD scenes with tileset structure")
print("- SSE (Screen Space Error) based LOD selection")
print("- Geometric error and distance range calculation")
print("- Automatic LOD switching based on camera distance")
print("- Real-time updates as you move the camera")
print("- Uses actual USDZ files with visibility control")
print("\nQuick Start:")
print("1. First, create USD scene using tileset_usd_creator.py:")
print("   - Run tileset_usd_creator.py in Script Editor")
print("   - This will create lod-demo-tileset.usd with tileset structure")
print("2. Then run this script: python simple_tileset_lod_example_standalone.py")
print("\nAvailable functions:")
print("  - start_tileset_lod()    # Start tileset LOD switching")
print("  - stop_tileset_lod()     # Stop tileset LOD switching")
print("\nOr call main() directly from the console")
print("=" * 60)

# 示例使用
if __name__ == "__main__":
    main()
