# Tile Index 排序和分批显示功能指南

## 功能概述

现在系统支持根据同一LOD级别下的`tile_index`进行排序，保证同一`tile_index`的节点可以同一批显示（渲染），顺序是按`tile_index`从小到大分批显示。

## 核心特性

### 1. Tile Index 排序
- **排序规则**: 首先按LOD级别排序，然后按`tile_index`从小到大排序
- **确保顺序**: 同一LOD级别下的瓦片严格按照`tile_index`顺序显示
- **控制台输出**: 显示详细的排序信息和显示顺序

### 2. 分批显示
- **批次控制**: 可配置每批显示的瓦片数量
- **时间间隔**: 可配置批次之间的时间间隔
- **顺序保证**: 确保瓦片按正确的`tile_index`顺序分批显示

### 3. 灵活配置
- **即时显示**: 可以禁用分批显示，立即显示所有瓦片
- **参数调优**: 支持不同的批次大小和时间间隔配置

## 配置选项

在`TilesetConfig`类中添加了以下配置选项：

```python
# 分批显示配置
self.enable_batch_display = False  # 是否启用分批显示
self.batch_size = 3  # 每批显示的瓦片数量
self.batch_interval = 0.15  # 批次间隔（秒）
```

## 使用方法

### 方法1: 使用配置类

```python
from simple_tileset_lod_example_standalone import TilesetConfig

# 创建配置
config = TilesetConfig()

# 启用分批显示
config.enable_batch_display = True
config.batch_size = 2  # 每批显示2个瓦片
config.batch_interval = 0.2  # 批次间隔0.2秒

# 应用配置（在LOD更新时会自动使用）
```

### 方法2: 使用测试脚本

```python
# 完整测试
from lod.test_tile_index_sorting import test
test()

# 快速测试
from lod.test_tile_index_sorting import quick
quick()
```

### 方法3: 直接调用更新函数

```python
from simple_tileset_lod_example_standalone import (
    update_tileset_lod_visibility_hierarchical,
    TilesetConfig
)

# 创建配置
config = TilesetConfig()
config.enable_batch_display = True
config.batch_size = 3
config.batch_interval = 0.15

# 调用更新函数
update_tileset_lod_visibility_hierarchical(
    stage, region_bounds, scheduler, verbose=True, config=config
)
```

## 控制台输出示例

启用详细输出后，您会看到类似的信息：

```
=== Updating Hierarchical Tileset LOD Visibility with Frustum Culling ===

📋 Content processing order (sorted by LOD level and tile_index):
   1. tile_index= 0, LOD=20, tile=Tile_0_0, content=Content_LOD_20
   2. tile_index= 1, LOD=20, tile=Tile_0_1, content=Content_LOD_20
   3. tile_index= 2, LOD=20, tile=Tile_0_2, content=Content_LOD_20

🔄 Starting batch display for 3 tiles (batch size: 2, interval: 0.2s)
📋 Tiles will be displayed in tile_index order:
   1. tile_index= 0, Content_LOD_20 at Tile_0_0
   2. tile_index= 1, Content_LOD_20 at Tile_0_1
   3. tile_index= 2, Content_LOD_20 at Tile_0_2

📦 Divided into 2 batches
📦 Batch 1/2: Showing tiles with tile_index [0, 1]
  ✅ Showing tile: Content_LOD_20 (tile_index: 0) at Tile_0_0
  ✅ Showing tile: Content_LOD_20 (tile_index: 1) at Tile_0_1
⏳ Waiting 0.2s before next batch...
📦 Batch 2/2: Showing tiles with tile_index [2]
  ✅ Showing tile: Content_LOD_20 (tile_index: 2) at Tile_0_2
✅ Batch display completed! All 3 tiles are now visible.
```

## 参数调优建议

### 批次大小 (batch_size)
- **1**: 最精确的顺序控制，每个瓦片单独显示
- **2-3**: 平衡的选择，既有顺序控制又有合理的显示速度
- **4-5**: 更快的显示速度，但顺序控制较粗糙

### 批次间隔 (batch_interval)
- **0.05-0.1s**: 快速显示，适合生产环境
- **0.15-0.3s**: 中等速度，适合演示和调试
- **0.5s以上**: 慢速显示，便于观察和验证

### 使用场景建议
- **生产环境**: `enable_batch_display = False` (即时显示)
- **演示场景**: `batch_size = 2-3, batch_interval = 0.2s`
- **调试验证**: `batch_size = 1, batch_interval = 0.5s`

## 技术实现细节

### 排序算法
```python
# 按LOD级别和tile_index排序
content_items.sort(key=lambda x: (x['lod_level'], x['tile_index']))
```

### 分批显示算法
1. **收集**: 收集所有需要显示的瓦片
2. **排序**: 按LOD级别和tile_index排序
3. **分批**: 将瓦片分成指定大小的批次
4. **显示**: 按时间间隔逐批显示瓦片

### 性能考虑
- 分批显示会增加总显示时间
- 适合需要精确顺序控制的场景
- 对于性能敏感的应用，建议禁用分批显示

## 故障排除

### 问题1: tile_index顺序不正确
- 检查USD文件中的`tileset:tileIndex`属性是否正确设置
- 验证瓦片创建时的tile_index分配逻辑

### 问题2: 分批显示不工作
- 确保`enable_batch_display = True`
- 检查瓦片数量是否大于1
- 验证配置是否正确传递给更新函数

### 问题3: 显示太慢或太快
- 调整`batch_interval`参数
- 调整`batch_size`参数
- 考虑禁用分批显示以获得最快速度

## 相关文件

- `simple_tileset_lod_example_standalone.py`: 核心实现
- `test_tile_index_sorting.py`: 测试脚本
- `tileset_usd_creator.py`: 瓦片创建时设置tile_index

## 总结

通过tile_index排序和分批显示功能，您可以：

1. **精确控制**: 瓦片按严格的tile_index顺序显示
2. **灵活配置**: 支持即时显示和分批显示两种模式
3. **可视化验证**: 详细的控制台输出帮助验证显示顺序
4. **性能平衡**: 可根据需求调整显示速度和顺序精度

推荐配置：
- 演示场景: `enable_batch_display = True, batch_size = 2, batch_interval = 0.2s`
- 生产环境: `enable_batch_display = False` (即时显示)
- 调试验证: `enable_batch_display = True, batch_size = 1, batch_interval = 0.5s`
