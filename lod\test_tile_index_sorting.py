"""
测试tile_index排序和分批显示功能
验证同一LOD级别下的瓦片按tile_index从小到大分批显示
"""

import sys
import os
import time

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

def test_tile_index_sorting_and_batch_display():
    """测试tile_index排序和分批显示功能"""
    print("=== Tile Index Sorting and Batch Display Test ===")
    
    try:
        # 导入必要的模块
        from simple_tileset_lod_example_standalone import (
            TilesetConfig,
            get_tileset_region_bounds_from_stage,
            update_tileset_lod_visibility_hierarchical,
            collect_all_tiles_from_stage
        )
        from lod_scheduler import LODScheduler
        import omni.usd
        
        # 获取当前stage
        stage = omni.usd.get_context().get_stage()
        if not stage:
            print("❌ ERROR: No stage available. Please load a USD scene first.")
            return False
        
        print("✅ Stage found")
        
        # 获取tileset区域边界
        region_bounds = get_tileset_region_bounds_from_stage()
        if not region_bounds:
            print("❌ ERROR: Could not find tileset region bounds in stage.")
            return False
        
        print("✅ Tileset region bounds found")
        
        # 分析当前瓦片结构
        all_tiles = collect_all_tiles_from_stage()
        if not all_tiles:
            print("❌ ERROR: No tiles found in stage.")
            return False
        
        print(f"✅ Found {len(all_tiles)} tiles in stage")
        
        # 分析瓦片的tile_index分布
        print(f"\n📊 Tile Structure Analysis:")
        tile_index_info = []
        for tile in all_tiles:
            # 获取tile_index属性
            tile_index = 0  # 默认值
            tile_prim = tile['prim']
            tile_index_attr = tile_prim.GetAttribute("tileset:tileIndex")
            if tile_index_attr:
                tile_index = tile_index_attr.Get() or 0
            
            depth = tile.get('depth', 0)
            content_count = len(tile.get('content_nodes', []))
            
            tile_index_info.append({
                'name': tile['name'],
                'tile_index': tile_index,
                'depth': depth,
                'content_count': content_count
            })
        
        # 按depth和tile_index排序显示
        tile_index_info.sort(key=lambda x: (x['depth'], x['tile_index']))
        
        depth_groups = {}
        for info in tile_index_info:
            depth = info['depth']
            if depth not in depth_groups:
                depth_groups[depth] = []
            depth_groups[depth].append(info)
        
        for depth in sorted(depth_groups.keys()):
            tiles_at_depth = depth_groups[depth]
            print(f"  Depth {depth}: {len(tiles_at_depth)} tiles")
            for info in tiles_at_depth:
                print(f"    {info['name']}: tile_index={info['tile_index']}, content_nodes={info['content_count']}")
        
        # 测试不同的分批显示配置
        test_configs = [
            {"enabled": False, "batch_size": 1, "interval": 0.1, "name": "No batch display (immediate)"},
            {"enabled": True, "batch_size": 1, "interval": 0.3, "name": "One tile per batch (slow)"},
            {"enabled": True, "batch_size": 2, "interval": 0.2, "name": "Two tiles per batch (medium)"},
            {"enabled": True, "batch_size": 3, "interval": 0.15, "name": "Three tiles per batch (fast)"},
        ]
        
        for i, test_config in enumerate(test_configs):
            print(f"\n" + "="*70)
            print(f"TEST {i+1}: {test_config['name']}")
            print("="*70)
            
            # 创建配置
            config = TilesetConfig()
            config.enable_batch_display = test_config["enabled"]
            config.batch_size = test_config["batch_size"]
            config.batch_interval = test_config["interval"]
            
            print(f"Configuration:")
            print(f"  Batch display enabled: {config.enable_batch_display}")
            print(f"  Batch size: {config.batch_size}")
            print(f"  Batch interval: {config.batch_interval}s")
            
            # 创建调度器
            scheduler = LODScheduler(stage, camera_path=config.camera_path, centralized_config=config.lod_config)
            scheduler.build_octree_from_tileset(config.tileset_path)
            
            print(f"\n🔧 Applying LOD update with tile_index sorting...")
            start_time = time.time()
            
            # 更新LOD（这里会应用tile_index排序和分批显示）
            update_tileset_lod_visibility_hierarchical(
                stage, region_bounds, scheduler, verbose=True, config=config
            )
            
            elapsed = time.time() - start_time
            print(f"✅ LOD update completed in {elapsed:.2f}s")
            
            print(f"\n💡 Expected behavior:")
            if config.enable_batch_display:
                print(f"   - Tiles should appear in batches of {config.batch_size}")
                print(f"   - Each batch should be separated by {config.batch_interval}s")
                print(f"   - Within each LOD level, tiles appear in tile_index order (0, 1, 2, ...)")
            else:
                print(f"   - All tiles appear immediately")
                print(f"   - Console shows tiles in tile_index order")
            
            if i < len(test_configs) - 1:  # 不是最后一个测试
                print(f"\nPress Enter to continue to next test...")
                input()
        
        print("\n" + "="*70)
        print("✅ Tile Index Sorting and Batch Display Test Completed!")
        print("="*70)
        print("💡 Key Features Demonstrated:")
        print("   1. Tiles are sorted by LOD level first, then by tile_index")
        print("   2. Same LOD level tiles appear in ascending tile_index order")
        print("   3. Batch display shows tiles in groups with configurable timing")
        print("   4. Console output shows the exact order of tile appearance")
        print("   5. Both immediate and batched display modes are supported")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def quick_test_tile_index_order():
    """快速测试tile_index顺序"""
    print("=== Quick Tile Index Order Test ===")
    
    try:
        from simple_tileset_lod_example_standalone import (
            TilesetConfig,
            get_tileset_region_bounds_from_stage,
            update_tileset_lod_visibility_hierarchical
        )
        from lod_scheduler import LODScheduler
        import omni.usd
        
        stage = omni.usd.get_context().get_stage()
        if not stage:
            print("❌ No stage available")
            return False
        
        region_bounds = get_tileset_region_bounds_from_stage()
        if not region_bounds:
            print("❌ No tileset region bounds found")
            return False
        
        config = TilesetConfig()
        config.enable_batch_display = True
        config.batch_size = 1  # 一次显示一个瓦片，最清楚地看到顺序
        config.batch_interval = 0.5  # 较长间隔，便于观察
        
        scheduler = LODScheduler(stage, camera_path=config.camera_path, centralized_config=config.lod_config)
        scheduler.build_octree_from_tileset(config.tileset_path)
        
        print("🔧 Testing tile_index order with batch_size=1, interval=0.5s")
        print("💡 Watch console output to see tile_index progression...")
        
        update_tileset_lod_visibility_hierarchical(
            stage, region_bounds, scheduler, verbose=True, config=config
        )
        
        print("💡 Expected: tile_index should appear in order: 0, 1, 2, 3, ...")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

# 便捷函数
def test():
    """运行完整测试"""
    return test_tile_index_sorting_and_batch_display()

def quick():
    """快速测试"""
    return quick_test_tile_index_order()

if __name__ == "__main__":
    print("Tile Index Sorting and Batch Display Test Script")
    print("Available functions:")
    print("  - test()     # Run complete tile index sorting and batch display test")
    print("  - quick()    # Quick test of tile_index order")
    print("\nTo run in Script Editor, call any of these functions.")
