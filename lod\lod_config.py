"""
LOD配置文件
用于管理LOD调度系统的各种参数和配置
"""

from enum import Enum
from dataclasses import dataclass
from typing import Dict, Any, Optional
import json
import os
import math

class LODLevel(Enum):
    """LOD级别枚举"""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    VERY_LOW = "very_low"

@dataclass
class LODConfig:
    """LOD配置类"""
    screen_error: float  # 屏幕误差阈值（像素）
    geometric_error: float = 0.0  # 几何误差（可选）
    max_triangles: int = 10000  # 最大三角形数量（可选）

@dataclass
class OctreeConfig:
    """八叉树配置类"""
    max_depth: int = 8  # 最大深度
    min_area_threshold: float = 100.0  # 最小面积阈值
    min_volume_threshold: float = 1000.0  # 最小体积阈值

class CentralizedLODConfig:
    """中心化LOD配置系统"""

    def __init__(self, tileset_path: Optional[str] = None):
        """
        初始化中心化LOD配置

        Args:
            tileset_path: tileset.json文件路径，如果提供则从中读取geometricError配置
        """
        self.tileset_path = tileset_path

        # 默认的LOD级别到几何误差的映射
        # 这些值会被tileset.json中的实际值覆盖
        self._default_geometric_errors = {
            32: "Low",      # geometricError >= 32 -> Low LOD
            16: "Medium",   # 16 <= geometricError < 32 -> Medium LOD
            8: "Medium",    # 8 <= geometricError < 16 -> Medium LOD
            4: "Medium",    # 4 <= geometricError < 8 -> Medium LOD
            2: "High",      # 2 <= geometricError < 4 -> High LOD
            1: "High",      # geometricError < 2 -> High LOD
        }

        # SSE配置
        self.maximum_screen_space_error = 32.0  # 最大可接受屏幕像素误差阈值
        self.screen_width = 1920  # 视口宽度（像素）
        self.screen_height = 1080  # 视口高度（像素）
        self.horizontal_fov = 60.0  # 水平视野角（度）

        # 相机配置
        self.camera_path = "/World/Camera"

        # 八叉树配置
        self.octree_config = OctreeConfig()

        # 从tileset.json加载实际的几何误差配置
        self._geometric_error_to_lod = {}
        self._lod_to_geometric_errors = {}
        if tileset_path:
            self._load_geometric_errors_from_tileset(tileset_path)
        else:
            self._use_default_geometric_errors()

    def _load_geometric_errors_from_tileset(self, tileset_path: str):
        """从tileset.json文件中加载几何误差配置"""
        try:
            if not os.path.exists(tileset_path):
                print(f"Warning: Tileset file not found: {tileset_path}, using default config")
                self._use_default_geometric_errors()
                return

            with open(tileset_path, 'r', encoding='utf-8') as f:
                tileset_data = json.load(f)

            # 递归收集所有的geometricError值
            geometric_errors = set()
            self._collect_geometric_errors(tileset_data.get('root', {}), geometric_errors)

            # 根据收集到的geometricError值建立映射
            sorted_errors = sorted(geometric_errors, reverse=True)  # 从大到小排序

            self._geometric_error_to_lod = {}
            self._lod_to_geometric_errors = {"High": [], "Medium": [], "Low": []}

            for error in sorted_errors:
                if error >= 16:
                    lod_level = "Low"
                elif error >= 4:
                    lod_level = "Medium"
                else:
                    lod_level = "High"

                self._geometric_error_to_lod[error] = lod_level
                self._lod_to_geometric_errors[lod_level].append(error)

            print(f"Loaded geometric errors from tileset: {sorted_errors}")
            print(f"LOD mapping: {self._geometric_error_to_lod}")

        except Exception as e:
            print(f"Error loading geometric errors from tileset: {e}")
            self._use_default_geometric_errors()

    def _collect_geometric_errors(self, tile_data: dict, errors: set):
        """递归收集tileset中的所有geometricError值"""
        if 'geometricError' in tile_data:
            errors.add(tile_data['geometricError'])

        # 递归处理子tiles
        for child in tile_data.get('children', []):
            self._collect_geometric_errors(child, errors)

    def _use_default_geometric_errors(self):
        """使用默认的几何误差配置"""
        self._geometric_error_to_lod = self._default_geometric_errors.copy()
        self._lod_to_geometric_errors = {
            "High": [1, 2],
            "Medium": [4, 8, 16],
            "Low": [32]
        }

    def get_lod_level_from_geometric_error(self, geometric_error: float) -> str:
        """根据几何误差获取LOD级别"""
        # 找到最接近的几何误差值
        closest_error = min(self._geometric_error_to_lod.keys(),
                           key=lambda x: abs(x - geometric_error))
        return self._geometric_error_to_lod[closest_error]

    def get_geometric_errors_for_lod(self, lod_level: str) -> list:
        """获取指定LOD级别对应的所有几何误差值"""
        return self._lod_to_geometric_errors.get(lod_level, [])

    def get_lod_level_numeric(self, geometric_error: float) -> int:
        """根据几何误差获取数字LOD级别"""
        # 使用几何误差直接计算数字LOD级别
        # 这个公式与tileset_usd_creator.py中的计算保持一致
        return int(20 - math.log2(max(geometric_error, 0.1)))

    def get_geometric_error_from_numeric_lod(self, numeric_lod: int) -> float:
        """根据数字LOD级别获取几何误差"""
        # 反向计算几何误差
        return 2 ** (20 - numeric_lod)

    def get_lod_name_from_numeric(self, numeric_lod: int) -> str:
        """根据数字LOD级别获取LOD名称"""
        if numeric_lod >= 19:
            return "High"
        elif numeric_lod >= 17:
            return "Medium"
        else:
            return "Low"

    def get_numeric_lod_from_name(self, lod_name: str) -> int:
        """根据LOD名称获取典型的数字级别"""
        name_to_numeric = {"High": 20, "Medium": 18, "Low": 15}
        return name_to_numeric.get(lod_name, 15)

    def get_all_geometric_errors(self) -> list:
        """获取所有几何误差值，按从小到大排序"""
        return sorted(self._geometric_error_to_lod.keys())

    def get_lod_mapping(self) -> dict:
        """获取几何误差到LOD级别的完整映射"""
        return self._geometric_error_to_lod.copy()

    def save_config(self, file_path: str):
        """保存配置到文件"""
        config_data = {
            "tileset_path": self.tileset_path,
            "maximum_screen_space_error": self.maximum_screen_space_error,
            "screen_width": self.screen_width,
            "screen_height": self.screen_height,
            "horizontal_fov": self.horizontal_fov,
            "camera_path": self.camera_path,
            "octree_config": {
                "max_depth": self.octree_config.max_depth,
                "min_area_threshold": self.octree_config.min_area_threshold,
                "min_volume_threshold": self.octree_config.min_volume_threshold
            },
            "geometric_error_to_lod": self._geometric_error_to_lod,
            "lod_to_geometric_errors": self._lod_to_geometric_errors
        }

        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)

        print(f"Configuration saved to {file_path}")

    def load_config(self, file_path: str):
        """从文件加载配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            self.tileset_path = config_data.get("tileset_path", self.tileset_path)
            self.maximum_screen_space_error = config_data.get("maximum_screen_space_error", self.maximum_screen_space_error)
            self.screen_width = config_data.get("screen_width", self.screen_width)
            self.screen_height = config_data.get("screen_height", self.screen_height)
            self.horizontal_fov = config_data.get("horizontal_fov", self.horizontal_fov)
            self.camera_path = config_data.get("camera_path", self.camera_path)

            octree_config = config_data.get("octree_config", {})
            self.octree_config.max_depth = octree_config.get("max_depth", self.octree_config.max_depth)
            self.octree_config.min_area_threshold = octree_config.get("min_area_threshold", self.octree_config.min_area_threshold)
            self.octree_config.min_volume_threshold = octree_config.get("min_volume_threshold", self.octree_config.min_volume_threshold)

            self._geometric_error_to_lod = config_data.get("geometric_error_to_lod", self._geometric_error_to_lod)
            self._lod_to_geometric_errors = config_data.get("lod_to_geometric_errors", self._lod_to_geometric_errors)

            print(f"Configuration loaded from {file_path}")

        except Exception as e:
            print(f"Error loading configuration from {file_path}: {e}")

# 创建全局默认配置实例
DEFAULT_LOD_CONFIG = CentralizedLODConfig()

# 便捷函数
def get_default_lod_config() -> CentralizedLODConfig:
    """获取默认LOD配置"""
    return DEFAULT_LOD_CONFIG

def create_lod_config_from_tileset(tileset_path: str) -> CentralizedLODConfig:
    """从tileset文件创建LOD配置"""
    return CentralizedLODConfig(tileset_path=tileset_path)

@dataclass
class CameraConfig:
    """相机配置类"""
    fov: float = 60.0  # 视场角（度）
    screen_width: int = 1920  # 屏幕宽度
    screen_height: int = 1080  # 屏幕高度
    near_plane: float = 0.1  # 近平面
    far_plane: float = 1000.0  # 远平面

@dataclass
class PerformanceConfig:
    """性能配置类"""
    max_tiles_per_frame: int = 1000  # 每帧最大瓦片数量
    update_interval: float = 0.1  # 更新间隔（秒）
    enable_frustum_culling: bool = True  # 启用视锥体剔除
    enable_occlusion_culling: bool = False  # 启用遮挡剔除

class LODSystemConfig:
    """LOD系统配置管理器（向后兼容）"""

    def __init__(self, centralized_config: Optional[CentralizedLODConfig] = None):
        # 使用中心化配置或创建默认配置
        self.centralized_config = centralized_config or get_default_lod_config()

        # 默认LOD配置（移除distance_threshold）
        self.lod_configs = {
            LODLevel.HIGH: LODConfig(
                screen_error=1.0,
                geometric_error=1.0,
                max_triangles=5000
            ),
            LODLevel.MEDIUM: LODConfig(
                screen_error=5.0,
                geometric_error=4.0,
                max_triangles=2000
            ),
            LODLevel.LOW: LODConfig(
                screen_error=15.0,
                geometric_error=8.0,
                max_triangles=500
            ),
            LODLevel.VERY_LOW: LODConfig(
                screen_error=50.0,
                geometric_error=16.0,
                max_triangles=100
            )
        }
        
        # 八叉树配置
        self.octree_config = OctreeConfig()
        
        # 相机配置
        self.camera_config = CameraConfig()
        
        # 性能配置
        self.performance_config = PerformanceConfig()
        
        # 其他配置
        self.coordinate_system_correction = True  # 是否应用坐标系修正
        self.debug_mode = False  # 调试模式
        self.log_level = "INFO"  # 日志级别
    
    def get_lod_config(self, level: LODLevel) -> LODConfig:
        """获取指定级别的LOD配置"""
        return self.lod_configs.get(level)
    
    def set_lod_config(self, level: LODLevel, config: LODConfig):
        """设置指定级别的LOD配置"""
        self.lod_configs[level] = config
    
    def get_optimal_lod_level(self, screen_error: float, geometric_error: float = None) -> LODLevel:
        """根据屏幕误差确定最优LOD级别（移除distance参数）"""
        # 如果提供了几何误差，使用中心化配置
        if geometric_error is not None:
            lod_name = self.centralized_config.get_lod_level_from_geometric_error(geometric_error)
            lod_map = {"High": LODLevel.HIGH, "Medium": LODLevel.MEDIUM, "Low": LODLevel.LOW}
            return lod_map.get(lod_name, LODLevel.LOW)

        # 否则使用传统的屏幕误差方法
        for level in [LODLevel.HIGH, LODLevel.MEDIUM, LODLevel.LOW, LODLevel.VERY_LOW]:
            config = self.lod_configs[level]
            if screen_error <= config.screen_error:
                return level
        return LODLevel.VERY_LOW
    
    def should_subdivide(self, screen_error: float, area: float, depth: int) -> bool:
        """判断是否应该细分"""
        return (depth < self.octree_config.max_depth and
                area > self.octree_config.min_area_threshold and
                screen_error > self.lod_configs[LODLevel.HIGH].screen_error)
    
    def save_config(self, file_path: str):
        """保存配置到文件"""
        config_data = {
            "lod_configs": {
                level.value: {
                    "screen_error": config.screen_error,
                    "geometric_error": config.geometric_error,
                    "max_triangles": config.max_triangles
                }
                for level, config in self.lod_configs.items()
            },
            "octree_config": {
                "max_depth": self.octree_config.max_depth,
                "min_area_threshold": self.octree_config.min_area_threshold,
                "min_volume_threshold": self.octree_config.min_volume_threshold
            },
            "camera_config": {
                "fov": self.camera_config.fov,
                "screen_width": self.camera_config.screen_width,
                "screen_height": self.camera_config.screen_height,
                "near_plane": self.camera_config.near_plane,
                "far_plane": self.camera_config.far_plane
            },
            "performance_config": {
                "max_tiles_per_frame": self.performance_config.max_tiles_per_frame,
                "update_interval": self.performance_config.update_interval,
                "enable_frustum_culling": self.performance_config.enable_frustum_culling,
                "enable_occlusion_culling": self.performance_config.enable_occlusion_culling
            },
            "other_config": {
                "coordinate_system_correction": self.coordinate_system_correction,
                "debug_mode": self.debug_mode,
                "log_level": self.log_level
            }
        }
        
        with open(file_path, 'w') as f:
            json.dump(config_data, f, indent=2)
        
        print(f"Configuration saved to {file_path}")
    
    def load_config(self, file_path: str):
        """从文件加载配置"""
        try:
            with open(file_path, 'r') as f:
                config_data = json.load(f)
            
            # 加载LOD配置
            for level_str, config_dict in config_data["lod_configs"].items():
                level = LODLevel(level_str)
                config = LODConfig(
                    screen_error=config_dict["screen_error"],
                    geometric_error=config_dict.get("geometric_error", 0.0),
                    max_triangles=config_dict.get("max_triangles", 10000)
                )
                self.lod_configs[level] = config
            
            # 加载八叉树配置
            octree_dict = config_data["octree_config"]
            self.octree_config = OctreeConfig(
                max_depth=octree_dict["max_depth"],
                min_area_threshold=octree_dict["min_area_threshold"],
                min_volume_threshold=octree_dict["min_volume_threshold"]
            )
            
            # 加载相机配置
            camera_dict = config_data["camera_config"]
            self.camera_config = CameraConfig(
                fov=camera_dict["fov"],
                screen_width=camera_dict["screen_width"],
                screen_height=camera_dict["screen_height"],
                near_plane=camera_dict["near_plane"],
                far_plane=camera_dict["far_plane"]
            )
            
            # 加载性能配置
            perf_dict = config_data["performance_config"]
            self.performance_config = PerformanceConfig(
                max_tiles_per_frame=perf_dict["max_tiles_per_frame"],
                update_interval=perf_dict["update_interval"],
                enable_frustum_culling=perf_dict["enable_frustum_culling"],
                enable_occlusion_culling=perf_dict["enable_occlusion_culling"]
            )
            
            # 加载其他配置
            other_dict = config_data["other_config"]
            self.coordinate_system_correction = other_dict["coordinate_system_correction"]
            self.debug_mode = other_dict["debug_mode"]
            self.log_level = other_dict["log_level"]
            
            print(f"Configuration loaded from {file_path}")
            
        except Exception as e:
            print(f"ERROR: Failed to load configuration from {file_path}: {e}")
    
    def get_default_config() -> 'LODSystemConfig':
        """获取默认配置"""
        return LODSystemConfig()
    
    def get_high_quality_config() -> 'LODSystemConfig':
        """获取高质量配置"""
        config = LODSystemConfig()

        # 更严格的LOD配置
        config.lod_configs[LODLevel.HIGH] = LODConfig(0.5, 0.5, 10000)
        config.lod_configs[LODLevel.MEDIUM] = LODConfig(2.0, 2.0, 5000)
        config.lod_configs[LODLevel.LOW] = LODConfig(8.0, 8.0, 2000)
        config.lod_configs[LODLevel.VERY_LOW] = LODConfig(25.0, 16.0, 500)
        
        # 更深的八叉树
        config.octree_config.max_depth = 10
        config.octree_config.min_area_threshold = 25.0
        
        return config
    
    def get_performance_config() -> 'LODSystemConfig':
        """获取性能优化配置"""
        config = LODSystemConfig()

        # 更宽松的LOD配置
        config.lod_configs[LODLevel.HIGH] = LODConfig(2.0, 2.0, 2000)
        config.lod_configs[LODLevel.MEDIUM] = LODConfig(10.0, 8.0, 1000)
        config.lod_configs[LODLevel.LOW] = LODConfig(25.0, 16.0, 500)
        config.lod_configs[LODLevel.VERY_LOW] = LODConfig(100.0, 32.0, 100)
        
        # 更浅的八叉树
        config.octree_config.max_depth = 6
        config.octree_config.min_area_threshold = 200.0
        
        # 性能优化
        config.performance_config.max_tiles_per_frame = 500
        config.performance_config.update_interval = 0.2
        
        return config

# 预定义配置
DEFAULT_CONFIG = LODSystemConfig.get_default_config()
HIGH_QUALITY_CONFIG = LODSystemConfig.get_high_quality_config()
PERFORMANCE_CONFIG = LODSystemConfig.get_performance_config()

def create_custom_config(
    screen_error_thresholds: Dict[str, float] = None,
    geometric_error_thresholds: Dict[str, float] = None,
    max_depth: int = 8,
    min_area_threshold: float = 100.0,
    tileset_path: str = None
) -> LODSystemConfig:
    """创建自定义配置"""
    # 创建中心化配置
    centralized_config = CentralizedLODConfig(tileset_path) if tileset_path else None
    config = LODSystemConfig(centralized_config)

    if screen_error_thresholds:
        for level_str, threshold in screen_error_thresholds.items():
            level = LODLevel(level_str)
            if level in config.lod_configs:
                config.lod_configs[level].screen_error = threshold

    if geometric_error_thresholds:
        for level_str, threshold in geometric_error_thresholds.items():
            level = LODLevel(level_str)
            if level in config.lod_configs:
                config.lod_configs[level].geometric_error = threshold

    config.octree_config.max_depth = max_depth
    config.octree_config.min_area_threshold = min_area_threshold

    return config