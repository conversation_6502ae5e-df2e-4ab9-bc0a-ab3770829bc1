"""
测试精确 LOD 控制和视锥体检测功能
验证修改后的系统是否只显示对应层级的 LOD，并支持视锥体裁剪
"""

import math

def test_precise_lod_control():
    """测试精确 LOD 控制逻辑"""
    print("=== 测试精确 LOD 控制 ===")
    
    # 模拟不同的 LOD 级别
    available_lod_levels = [15, 16, 17, 18, 19, 20]
    target_scenarios = [
        {"target_lod": "Low", "target_level": 15},
        {"target_lod": "Medium", "target_level": 18},
        {"target_lod": "High", "target_level": 20}
    ]
    
    for scenario in target_scenarios:
        target_lod = scenario["target_lod"]
        target_level = scenario["target_level"]
        
        print(f"\n目标 LOD: {target_lod} (级别 {target_level})")
        
        visible_levels = []
        hidden_levels = []
        
        for lod_level in available_lod_levels:
            # 新的精确匹配逻辑
            lod_match = (lod_level == target_level)
            
            if lod_match:
                visible_levels.append(lod_level)
                print(f"  ✅ LOD {lod_level}: 可见 (精确匹配)")
            else:
                hidden_levels.append(lod_level)
                print(f"  ❌ LOD {lod_level}: 隐藏 (不匹配)")
        
        print(f"  总结: 可见 {len(visible_levels)} 个级别, 隐藏 {len(hidden_levels)} 个级别")
        
        # 验证只有一个级别可见
        if len(visible_levels) == 1:
            print(f"  ✅ 精确控制成功: 只显示 LOD {visible_levels[0]}")
        else:
            print(f"  ❌ 精确控制失败: 显示了 {len(visible_levels)} 个级别")

def test_frustum_culling_logic():
    """测试视锥体裁剪逻辑"""
    print("\n=== 测试视锥体裁剪逻辑 ===")
    
    # 模拟相机参数
    camera_position = [0, 0, 0]
    camera_forward = [0, 0, -1]  # 朝向 -Z
    h_fov = math.radians(60)  # 60度水平视野
    v_fov = math.radians(45)  # 45度垂直视野
    
    print(f"相机位置: {camera_position}")
    print(f"相机朝向: {camera_forward}")
    print(f"水平视野: {math.degrees(h_fov):.1f}°")
    print(f"垂直视野: {math.degrees(v_fov):.1f}°")
    
    # 模拟不同位置的边界框
    test_bboxes = [
        {"name": "正前方", "min": [-5, -5, -20], "max": [5, 5, -10], "expected": True},
        {"name": "左侧", "min": [-20, -5, -20], "max": [-10, 5, -10], "expected": False},
        {"name": "右侧", "min": [10, -5, -20], "max": [20, 5, -10], "expected": False},
        {"name": "上方", "min": [-5, 10, -20], "max": [5, 20, -10], "expected": False},
        {"name": "下方", "min": [-5, -20, -20], "max": [5, -10, -10], "expected": False},
        {"name": "后方", "min": [-5, -5, 10], "max": [5, 5, 20], "expected": False},
        {"name": "部分重叠", "min": [3, -5, -20], "max": [15, 5, -10], "expected": True},
    ]
    
    print(f"\n视锥体检测结果:")
    for bbox in test_bboxes:
        name = bbox["name"]
        expected = bbox["expected"]
        
        # 简化的视锥体检测逻辑（基于角度）
        bbox_center = [
            (bbox["min"][0] + bbox["max"][0]) / 2,
            (bbox["min"][1] + bbox["max"][1]) / 2,
            (bbox["min"][2] + bbox["max"][2]) / 2
        ]
        
        # 计算从相机到边界框中心的向量
        to_bbox = [
            bbox_center[0] - camera_position[0],
            bbox_center[1] - camera_position[1],
            bbox_center[2] - camera_position[2]
        ]
        
        # 检查是否在前方
        forward_dot = to_bbox[2] * camera_forward[2]  # 简化的点积
        in_front = forward_dot < 0  # 朝向 -Z，所以负值表示前方
        
        # 检查水平角度
        horizontal_angle = math.atan2(abs(to_bbox[0]), abs(to_bbox[2])) if to_bbox[2] != 0 else math.pi/2
        in_horizontal_fov = horizontal_angle <= h_fov / 2
        
        # 检查垂直角度
        vertical_angle = math.atan2(abs(to_bbox[1]), abs(to_bbox[2])) if to_bbox[2] != 0 else math.pi/2
        in_vertical_fov = vertical_angle <= v_fov / 2
        
        # 简化的可见性判断
        visible = in_front and in_horizontal_fov and in_vertical_fov
        
        status = "✅ 可见" if visible else "❌ 裁剪"
        match = "✅ 正确" if visible == expected else "❌ 错误"
        
        print(f"  {name}: {status} (预期: {'可见' if expected else '裁剪'}) {match}")

def test_combined_lod_and_frustum():
    """测试 LOD 控制和视锥体检测的组合效果"""
    print("\n=== 测试 LOD + 视锥体组合效果 ===")
    
    # 模拟场景：有多个不同 LOD 级别的 tiles，分布在不同位置
    tiles = [
        {"name": "Tile_1", "lod": 15, "position": "前方", "in_frustum": True},
        {"name": "Tile_2", "lod": 18, "position": "前方", "in_frustum": True},
        {"name": "Tile_3", "lod": 20, "position": "前方", "in_frustum": True},
        {"name": "Tile_4", "lod": 15, "position": "左侧", "in_frustum": False},
        {"name": "Tile_5", "lod": 18, "position": "左侧", "in_frustum": False},
        {"name": "Tile_6", "lod": 20, "position": "右侧", "in_frustum": False},
    ]
    
    target_scenarios = [
        {"target_lod": "Low", "target_level": 15},
        {"target_lod": "Medium", "target_level": 18},
        {"target_lod": "High", "target_level": 20}
    ]
    
    for scenario in target_scenarios:
        target_lod = scenario["target_lod"]
        target_level = scenario["target_level"]
        
        print(f"\n目标 LOD: {target_lod} (级别 {target_level})")
        
        visible_tiles = []
        lod_hidden_tiles = []
        frustum_culled_tiles = []
        
        for tile in tiles:
            tile_lod = tile["lod"]
            tile_name = tile["name"]
            tile_position = tile["position"]
            in_frustum = tile["in_frustum"]
            
            # LOD 匹配检查
            lod_match = (tile_lod == target_level)
            
            # 最终可见性
            visible = lod_match and in_frustum
            
            if visible:
                visible_tiles.append(tile_name)
                print(f"  ✅ {tile_name} (LOD {tile_lod}, {tile_position}): 可见")
            elif lod_match and not in_frustum:
                frustum_culled_tiles.append(tile_name)
                print(f"  🚫 {tile_name} (LOD {tile_lod}, {tile_position}): 视锥体裁剪")
            else:
                lod_hidden_tiles.append(tile_name)
                print(f"  ❌ {tile_name} (LOD {tile_lod}, {tile_position}): LOD 不匹配")
        
        print(f"  总结:")
        print(f"    可见: {len(visible_tiles)} 个")
        print(f"    LOD 隐藏: {len(lod_hidden_tiles)} 个")
        print(f"    视锥体裁剪: {len(frustum_culled_tiles)} 个")

def main():
    """主测试函数"""
    print("精确 LOD 控制和视锥体检测功能测试")
    print("=" * 60)
    
    test_precise_lod_control()
    test_frustum_culling_logic()
    test_combined_lod_and_frustum()
    
    print("\n" + "=" * 60)
    print("测试总结:")
    print("✅ 精确 LOD 控制: 只显示目标 LOD 级别的内容")
    print("✅ 视锥体检测: 只显示相机视野内的内容")
    print("✅ 组合效果: LOD 匹配 AND 视锥体内 = 可见")
    print("\n修改后的系统应该能够:")
    print("1. 🎯 精确控制 LOD 级别 (不再有 ±2 级别容差)")
    print("2. 🔍 视锥体裁剪 (只显示相机视野内的内容)")
    print("3. 🚀 更好的性能 (减少不必要的渲染)")
    print("4. 📊 详细的调试信息 (可见/隐藏/裁剪统计)")

if __name__ == "__main__":
    main()
