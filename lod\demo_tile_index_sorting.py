"""
演示tile_index排序和分批显示功能
简单易用的演示脚本
"""

import sys
import os

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

def demo_tile_index_sorting():
    """演示tile_index排序功能"""
    print("🎯 Tile Index Sorting Demo")
    print("=" * 50)
    
    try:
        from simple_tileset_lod_example_standalone import (
            TilesetConfig,
            get_tileset_region_bounds_from_stage,
            update_tileset_lod_visibility_hierarchical
        )
        from lod_scheduler import LODScheduler
        import omni.usd
        
        # 检查stage
        stage = omni.usd.get_context().get_stage()
        if not stage:
            print("❌ 请先加载一个包含tileset的USD场景")
            return False
        
        # 检查tileset区域
        region_bounds = get_tileset_region_bounds_from_stage()
        if not region_bounds:
            print("❌ 未找到tileset区域，请确保场景包含TilesetRegion")
            return False
        
        print("✅ 场景检查通过")
        
        # 创建配置 - 启用分批显示
        config = TilesetConfig()
        config.enable_batch_display = True
        config.batch_size = 2  # 每批显示2个瓦片
        config.batch_interval = 0.3  # 批次间隔0.3秒
        
        print(f"🔧 配置:")
        print(f"   分批显示: {config.enable_batch_display}")
        print(f"   批次大小: {config.batch_size}")
        print(f"   批次间隔: {config.batch_interval}s")
        
        # 创建调度器
        scheduler = LODScheduler(stage, camera_path=config.camera_path, centralized_config=config.lod_config)
        scheduler.build_octree_from_tileset(config.tileset_path)
        
        print(f"\n🚀 开始演示tile_index排序和分批显示...")
        print(f"💡 观察控制台输出，瓦片将按tile_index顺序分批显示")
        
        # 执行LOD更新
        update_tileset_lod_visibility_hierarchical(
            stage, region_bounds, scheduler, verbose=True, config=config
        )
        
        print(f"\n✅ 演示完成!")
        print(f"💡 瓦片已按tile_index顺序分批显示")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def demo_immediate_display():
    """演示即时显示（不分批）"""
    print("⚡ Immediate Display Demo")
    print("=" * 50)
    
    try:
        from simple_tileset_lod_example_standalone import (
            TilesetConfig,
            get_tileset_region_bounds_from_stage,
            update_tileset_lod_visibility_hierarchical
        )
        from lod_scheduler import LODScheduler
        import omni.usd
        
        stage = omni.usd.get_context().get_stage()
        if not stage:
            print("❌ 请先加载一个包含tileset的USD场景")
            return False
        
        region_bounds = get_tileset_region_bounds_from_stage()
        if not region_bounds:
            print("❌ 未找到tileset区域")
            return False
        
        print("✅ 场景检查通过")
        
        # 创建配置 - 禁用分批显示
        config = TilesetConfig()
        config.enable_batch_display = False  # 即时显示
        
        print(f"🔧 配置:")
        print(f"   分批显示: {config.enable_batch_display}")
        print(f"   显示模式: 即时显示所有瓦片")
        
        scheduler = LODScheduler(stage, camera_path=config.camera_path, centralized_config=config.lod_config)
        scheduler.build_octree_from_tileset(config.tileset_path)
        
        print(f"\n⚡ 开始演示即时显示...")
        print(f"💡 所有瓦片将立即显示，但仍按tile_index排序")
        
        update_tileset_lod_visibility_hierarchical(
            stage, region_bounds, scheduler, verbose=True, config=config
        )
        
        print(f"\n✅ 演示完成!")
        print(f"💡 所有瓦片已立即显示")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        return False

def demo_single_tile_batches():
    """演示单瓦片批次（最精确的顺序控制）"""
    print("🎯 Single Tile Batch Demo")
    print("=" * 50)
    
    try:
        from simple_tileset_lod_example_standalone import (
            TilesetConfig,
            get_tileset_region_bounds_from_stage,
            update_tileset_lod_visibility_hierarchical
        )
        from lod_scheduler import LODScheduler
        import omni.usd
        
        stage = omni.usd.get_context().get_stage()
        if not stage:
            print("❌ 请先加载一个包含tileset的USD场景")
            return False
        
        region_bounds = get_tileset_region_bounds_from_stage()
        if not region_bounds:
            print("❌ 未找到tileset区域")
            return False
        
        print("✅ 场景检查通过")
        
        # 创建配置 - 单瓦片批次
        config = TilesetConfig()
        config.enable_batch_display = True
        config.batch_size = 1  # 每批只显示1个瓦片
        config.batch_interval = 0.5  # 较长间隔，便于观察
        
        print(f"🔧 配置:")
        print(f"   分批显示: {config.enable_batch_display}")
        print(f"   批次大小: {config.batch_size} (单瓦片)")
        print(f"   批次间隔: {config.batch_interval}s")
        
        scheduler = LODScheduler(stage, camera_path=config.camera_path, centralized_config=config.lod_config)
        scheduler.build_octree_from_tileset(config.tileset_path)
        
        print(f"\n🎯 开始演示单瓦片批次显示...")
        print(f"💡 每个瓦片将单独显示，间隔{config.batch_interval}秒")
        print(f"💡 这是最精确的tile_index顺序控制")
        
        update_tileset_lod_visibility_hierarchical(
            stage, region_bounds, scheduler, verbose=True, config=config
        )
        
        print(f"\n✅ 演示完成!")
        print(f"💡 瓦片已按严格的tile_index顺序逐个显示")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        return False

def show_tile_structure():
    """显示当前场景的瓦片结构"""
    print("📊 Tile Structure Analysis")
    print("=" * 50)
    
    try:
        from simple_tileset_lod_example_standalone import collect_all_tiles_from_stage
        import omni.usd
        
        stage = omni.usd.get_context().get_stage()
        if not stage:
            print("❌ 请先加载一个包含tileset的USD场景")
            return False
        
        all_tiles = collect_all_tiles_from_stage()
        if not all_tiles:
            print("❌ 未找到瓦片")
            return False
        
        print(f"✅ 找到 {len(all_tiles)} 个瓦片")
        
        # 分析瓦片结构
        tile_info = []
        for tile in all_tiles:
            # 获取tile_index
            tile_index = 0
            tile_prim = tile['prim']
            tile_index_attr = tile_prim.GetAttribute("tileset:tileIndex")
            if tile_index_attr:
                tile_index = tile_index_attr.Get() or 0
            
            depth = tile.get('depth', 0)
            content_nodes = tile.get('content_nodes', [])
            
            for content in content_nodes:
                lod_level = content.get('lod_level', 'Unknown')
                tile_info.append({
                    'tile_name': tile['name'],
                    'tile_index': tile_index,
                    'depth': depth,
                    'lod_level': lod_level,
                    'content_name': content['name']
                })
        
        # 按depth, lod_level, tile_index排序
        tile_info.sort(key=lambda x: (x['depth'], x['lod_level'], x['tile_index']))
        
        print(f"\n📋 瓦片结构 (按depth, LOD, tile_index排序):")
        print(f"{'序号':<4} {'Depth':<6} {'LOD':<6} {'TileIndex':<10} {'瓦片名称':<15} {'内容名称'}")
        print("-" * 80)
        
        for i, info in enumerate(tile_info, 1):
            print(f"{i:<4} {info['depth']:<6} {info['lod_level']:<6} {info['tile_index']:<10} "
                  f"{info['tile_name']:<15} {info['content_name']}")
        
        print(f"\n💡 这就是瓦片将要显示的顺序")
        print(f"💡 同一LOD级别下，tile_index从小到大排序")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

# 便捷函数
def demo():
    """运行分批显示演示"""
    return demo_tile_index_sorting()

def immediate():
    """运行即时显示演示"""
    return demo_immediate_display()

def single():
    """运行单瓦片批次演示"""
    return demo_single_tile_batches()

def structure():
    """显示瓦片结构"""
    return show_tile_structure()

if __name__ == "__main__":
    print("Tile Index Sorting Demo Script")
    print("=" * 50)
    print("Available functions:")
    print("  - demo()      # 分批显示演示 (推荐)")
    print("  - immediate() # 即时显示演示")
    print("  - single()    # 单瓦片批次演示")
    print("  - structure() # 显示瓦片结构")
    print("\n💡 建议先运行 structure() 查看瓦片结构")
    print("💡 然后运行 demo() 查看分批显示效果")
